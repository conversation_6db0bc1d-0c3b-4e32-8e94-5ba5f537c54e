<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "user_permission".
 *
 * @property int $id
 * @property int $user_id
 * @property string $route
 * @property int $can_access
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property User $user
 */
class UserPermission extends \yii\db\ActiveRecord
{

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'user_permission';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['can_access'], 'default', 'value' => 1],
            [['user_id', 'route'], 'required'],
            [['user_id', 'can_access'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['route'], 'string', 'max' => 255],
            [['route'], 'validateRoutes'], // Add custom validation
            [['user_id', 'route'], 'unique', 'targetAttribute' => ['user_id', 'route']],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['user_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'route' => 'Route',
            'can_access' => 'Can Access',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }

    /**
     * Custom validation for routes (handles both string and array)
     */
    public function validateRoutes($attribute, $params)
    {
        if (is_array($this->$attribute)) {
            foreach ($this->$attribute as $route) {
                if (!is_string($route) || strlen($route) > 255) {
                    $this->addError($attribute, 'Each route must be a string with maximum 255 characters.');
                    break;
                }
            }
        }
    }

    /**
     * Override beforeSave to handle multiple routes
     */
    public function beforeSave($insert)
    {
        if (parent::beforeSave($insert)) {
            // If route is an array, we'll handle it in the controller
            return true;
        }
        return false;
    }

    public function afterSave($insert, $changedAttributes)
    {
        parent::afterSave($insert, $changedAttributes);
        $this->refreshUserCache();
    }

    public function afterDelete()
    {
        parent::afterDelete();
        $this->refreshUserCache();
    }

    private function refreshUserCache()
    {
        $userId = $this->user_id;
        Yii::$app->cache->delete("user_permissions_{$userId}");

        // Rebuild cache immediately by calling getCachedPermissions
        $helper = new \common\components\AccessHelper();
        (new \ReflectionClass($helper))
            ->getMethod('getCachedPermissions')
            ->invokeArgs($helper, [$userId, \common\models\User::findOne($userId)->role_id]);
    }
}
