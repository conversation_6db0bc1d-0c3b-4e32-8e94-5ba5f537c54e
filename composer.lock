{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "fdeb04059aa3af3b75ede11608d828e6", "packages": [{"name": "2amigos/yii2-chartjs-widget", "version": "2.1.3", "source": {"type": "git", "url": "https://github.com/2amigos/yii2-chartjs-widget.git", "reference": "b144a4ebd923872a7263c980f8afe2e212378275"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/2amigos/yii2-chartjs-widget/zipball/b144a4ebd923872a7263c980f8afe2e212378275", "reference": "b144a4ebd923872a7263c980f8afe2e212378275", "shasum": ""}, "require": {"bower-asset/chartjs": "~2.6", "yiisoft/yii2": "~2.0.5"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "phpunit/phpunit": "~4.4"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"dosamigos\\chartjs\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "2amigOS! Consulting Group", "email": "<EMAIL>", "homepage": "http://2amigos.us", "role": "Developer"}], "description": "ChartJs widget for Yii2.", "homepage": "http://yiiwheels.com/extension/chartjs-widget", "keywords": ["2amigos", "chartjs", "extension", "widget", "yii", "yii 2", "yii2"], "support": {"issues": "https://github.com/2amigos/yii2-chartjs-widget/issues", "source": "https://github.com/2amigos/yii2-chartjs-widget"}, "abandoned": true, "time": "2018-05-02T17:59:16+00:00"}, {"name": "2amigos/yii2-ckeditor-widget", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/2amigos/yii2-ckeditor-widget.git", "reference": "2ba96ec670d0745b5616d2aa94d720cc7288bc08"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/2amigos/yii2-ckeditor-widget/zipball/2ba96ec670d0745b5616d2aa94d720cc7288bc08", "reference": "2ba96ec670d0745b5616d2aa94d720cc7288bc08", "shasum": ""}, "require": {"ckeditor/ckeditor": "~4.7.0+full", "yiisoft/yii2": "^2.0"}, "require-dev": {"phpunit/phpunit": "4.*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"dosamigos\\ckeditor\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "2amigOS! Consulting Group", "email": "<EMAIL>", "homepage": "http://2amigos.us", "role": "Developer"}], "description": "CKEditor widget for Yii2.", "homepage": "http://yiiwheels.com/extension/ckeditor-widget", "keywords": ["2amigos", "CKEditor", "extension", "widget", "yii", "yii 2", "yii2"], "support": {"issues": "https://github.com/2amigos/yii2-ckeditor-widget/issues", "source": "https://github.com/2amigos/yii2-ckeditor-widget"}, "abandoned": true, "time": "2017-06-12T18:51:26+00:00"}, {"name": "almasaeed2010/adminlte", "version": "v3.2.0", "source": {"type": "git", "url": "https://github.com/ColorlibHQ/AdminLTE.git", "reference": "bd4d9c72931f1dd28601b6bfb387554a381ad540"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ColorlibHQ/AdminLTE/zipball/bd4d9c72931f1dd28601b6bfb387554a381ad540", "reference": "bd4d9c72931f1dd28601b6bfb387554a381ad540", "shasum": ""}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Colorlib"}], "description": "AdminLTE - admin control panel and dashboard that's based on Bootstrap 4", "homepage": "https://adminlte.io/", "keywords": ["JS", "admin", "back-end", "css", "less", "responsive", "template", "theme", "web"], "support": {"issues": "https://github.com/ColorlibHQ/AdminLTE/issues", "source": "https://github.com/ColorlibHQ/AdminLTE/tree/v3.2.0"}, "time": "2022-02-07T20:33:09+00:00"}, {"name": "bedezign/yii2-audit", "version": "1.2.7", "source": {"type": "git", "url": "https://github.com/bedezign/yii2-audit.git", "reference": "6f97ac9af94a7fd08e1e5411e8e105b15e250356"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bedezign/yii2-audit/zipball/6f97ac9af94a7fd08e1e5411e8e105b15e250356", "reference": "6f97ac9af94a7fd08e1e5411e8e105b15e250356", "shasum": ""}, "require": {"2amigos/yii2-chartjs-widget": ">=2.0.1 <3.0.0", "ext-bcmath": "*", "php": ">=7.2.0", "phpspec/php-diff": "1.*", "yiisoft/yii2": ">=2.0.0 <2.1.0", "yiisoft/yii2-debug": ">=2.1.0 <2.2.0"}, "suggest": {"php-mime-mail-parser/php-mime-mail-parser": "Allows better displaying of logged emails."}, "type": "yii2-extension", "extra": {"bootstrap": "bedezign\\yii2\\audit\\Bootstrap"}, "autoload": {"psr-4": {"bedezign\\yii2\\audit\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<PERSON>@bedezign.com", "homepage": "http://bedezign.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://mrphp.com.au/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://maruc.nl/"}], "description": "Yii2 Audit records and displays web/cli requests, database changes, php/js errors and associated data.", "keywords": ["Audit", "logging", "trail", "yii2"], "support": {"issues": "https://github.com/bedezign/yii2-audit/issues", "source": "https://github.com/bedezign/yii2-audit/tree/1.2.7"}, "time": "2024-04-08T15:29:07+00:00"}, {"name": "biladina/yii2-ajax<PERSON><PERSON>-bs4", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/biladina/yii2-ajaxcrud.git", "reference": "9162994807244d3db6228c9f5be085f36ec9f3c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/biladina/yii2-ajaxcrud/zipball/9162994807244d3db6228c9f5be085f36ec9f3c1", "reference": "9162994807244d3db6228c9f5be085f36ec9f3c1", "shasum": ""}, "require": {"fortawesome/font-awesome": "^5.15", "kartik-v/yii2-bootstrap5-dropdown": "@dev", "kartik-v/yii2-editable": "^1.7.3", "kartik-v/yii2-grid": "^3.0.4", "kartik-v/yii2-mpdf": "^1.0.0", "yiisoft/yii2": "*", "yiisoft/yii2-bootstrap5": "*", "yiisoft/yii2-gii": "*"}, "type": "yii2-extension", "extra": {"bootstrap": "yii2ajaxcrud\\ajaxcrud\\Bootstrap"}, "autoload": {"psr-4": {"yii2ajaxcrud\\ajaxcrud\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/johnitvn?tab=repositories"}, {"name": "hoaaah", "homepage": "https://github.com/hoaaah"}, {"name": "Biladia Inc.", "homepage": "https://github.com/biladina"}], "description": "Gii CRUD template for Single Page Ajax Administration for yii2", "keywords": ["ajax", "crud", "database", "extension", "gii", "template", "yii2"], "support": {"issues": "https://github.com/biladina/yii2-ajaxcrud-bs4/issues?state=open", "source": "https://github.com/biladina/yii2-ajaxcrud-bs4"}, "time": "2023-06-22T09:21:52+00:00"}, {"name": "bower-asset/bootstrap", "version": "v5.3.7", "source": {"type": "git", "url": "https://github.com/twbs/bootstrap.git", "reference": "e0032ae6a5a628a51a8552091816cec09b6434df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twbs/bootstrap/zipball/e0032ae6a5a628a51a8552091816cec09b6434df", "reference": "e0032ae6a5a628a51a8552091816cec09b6434df"}, "type": "bower-asset"}, {"name": "bower-asset/chartjs", "version": "v2.9.4", "source": {"type": "git", "url": "https://github.com/chartjs/Chart.js.git", "reference": "9bd4cf82fda9f50a5fb50b72843e06ab88124278"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/chartjs/Chart.js/zipball/9bd4cf82fda9f50a5fb50b72843e06ab88124278", "reference": "9bd4cf82fda9f50a5fb50b72843e06ab88124278"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/fontawesome", "version": "v4.3.0", "source": {"type": "git", "url": "https://github.com/FortAwesome/Font-Awesome.git", "reference": "41b9ed01103e6820c3cb043ba7ddab30ecd3f4c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FortAwesome/Font-Awesome/zipball/41b9ed01103e6820c3cb043ba7ddab30ecd3f4c0", "reference": "41b9ed01103e6820c3cb043ba7ddab30ecd3f4c0"}, "type": "bower-asset", "license": ["OFL-1.1", "MIT", "CC-BY-3.0"]}, {"name": "bower-asset/inputmask", "version": "5.0.9", "source": {"type": "git", "url": "https://github.com/RobinHerbots/Inputmask.git", "reference": "310a33557e2944daf86d5946a5e8c82b9118f8f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RobinHerbots/Inputmask/zipball/310a33557e2944daf86d5946a5e8c82b9118f8f7", "reference": "310a33557e2944daf86d5946a5e8c82b9118f8f7"}, "require": {"bower-asset/jquery": ">=1.7"}, "type": "bower-asset", "license": ["http://opensource.org/licenses/mit-license.php"]}, {"name": "bower-asset/jquery", "version": "3.7.1", "source": {"type": "git", "url": "https://github.com/jquery/jquery-dist.git", "reference": "fde1f76e2799dd877c176abde0ec836553246991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jquery/jquery-dist/zipball/fde1f76e2799dd877c176abde0ec836553246991", "reference": "fde1f76e2799dd877c176abde0ec836553246991"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/jquery-ui", "version": "1.12.1", "source": {"type": "git", "url": "**************:components/jqueryui.git", "reference": "44ecf3794cc56b65954cc19737234a3119d036cc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/components/jqueryui/zipball/44ecf3794cc56b65954cc19737234a3119d036cc", "reference": "44ecf3794cc56b65954cc19737234a3119d036cc"}, "require": {"bower-asset/jquery": ">=1.6"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/punycode", "version": "v1.4.1", "source": {"type": "git", "url": "https://github.com/mathiasbynens/punycode.js.git", "reference": "0fbadd6e81f3a0ce06c38998040d6db6bdfbc5c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mathiasbynens/punycode.js/zipball/0fbadd6e81f3a0ce06c38998040d6db6bdfbc5c9", "reference": "0fbadd6e81f3a0ce06c38998040d6db6bdfbc5c9"}, "type": "bower-asset"}, {"name": "bower-asset/sweetalert2", "version": "v11.7.3", "source": {"type": "git", "url": "**************:sweetalert2/sweetalert2.git", "reference": "dd93e194730becd4f14b248f9f507257701c5db9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sweetalert2/sweetalert2/zipball/dd93e194730becd4f14b248f9f507257701c5db9", "reference": "dd93e194730becd4f14b248f9f507257701c5db9"}, "type": "bower-asset"}, {"name": "bower-asset/vis", "version": "v4.21.0", "source": {"type": "git", "url": "**************:almende/vis.git", "reference": "001716727826e2ba6cc0df7070733013936dda9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/almende/vis/zipball/001716727826e2ba6cc0df7070733013936dda9a", "reference": "001716727826e2ba6cc0df7070733013936dda9a"}, "type": "bower-asset", "license": ["Apache-2.0", "MIT"]}, {"name": "bower-asset/yii2-pjax", "version": "2.0.8", "source": {"type": "git", "url": "https://github.com/yiisoft/jquery-pjax.git", "reference": "a9298d57da63d14a950f1b94366a864bc62264fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/jquery-pjax/zipball/a9298d57da63d14a950f1b94366a864bc62264fb", "reference": "a9298d57da63d14a950f1b94366a864bc62264fb"}, "require": {"bower-asset/jquery": ">=1.8"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "cebe/markdown", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/cebe/markdown.git", "reference": "9bac5e971dd391e2802dca5400bbeacbaea9eb86"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cebe/markdown/zipball/9bac5e971dd391e2802dca5400bbeacbaea9eb86", "reference": "9bac5e971dd391e2802dca5400bbeacbaea9eb86", "shasum": ""}, "require": {"lib-pcre": "*", "php": ">=5.4.0"}, "require-dev": {"cebe/indent": "*", "facebook/xhprof": "*@dev", "phpunit/phpunit": "4.1.*"}, "bin": ["bin/markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"cebe\\markdown\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Creator"}], "description": "A super fast, highly extensible markdown parser for PHP", "homepage": "https://github.com/cebe/markdown#readme", "keywords": ["extensible", "fast", "gfm", "markdown", "markdown-extra"], "support": {"issues": "https://github.com/cebe/markdown/issues", "source": "https://github.com/cebe/markdown"}, "time": "2018-03-26T11:24:36+00:00"}, {"name": "ckeditor/ckeditor", "version": "4.7.3", "source": {"type": "git", "url": "https://github.com/ckeditor/ckeditor4-releases.git", "reference": "97b8f412f6b36985c734daf0717e81009a885dba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ckeditor/ckeditor4-releases/zipball/97b8f412f6b36985c734daf0717e81009a885dba", "reference": "97b8f412f6b36985c734daf0717e81009a885dba", "shasum": ""}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0+", "LGPL-2.1+", "MPL-1.1+"], "authors": [{"name": "CKSource", "homepage": "http://cksource.com"}], "description": "JavaScript WYSIWYG web text editor.", "homepage": "http://ckeditor.com", "keywords": ["CKEditor", "editor", "f<PERSON>itor", "html", "javascript", "richtext", "text", "wysiwyg"], "support": {"forum": "http://ckeditor.com/forums", "issues": "http://dev.ckeditor.com", "source": "http://github.com/ckeditor/ckeditor-dev", "wiki": "http://docs.ckeditor.com"}, "time": "2017-09-13T13:09:47+00:00"}, {"name": "components/flag-icon-css", "version": "v7.5.0", "source": {"type": "git", "url": "https://github.com/lipis/flag-icons.git", "reference": "7aa5b2bdddd570ece62c812c0cb588ccdc099e2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lipis/flag-icons/zipball/7aa5b2bdddd570ece62c812c0cb588ccdc099e2e", "reference": "7aa5b2bdddd570ece62c812c0cb588ccdc099e2e", "shasum": ""}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Panay<PERSON><PERSON>", "email": "<EMAIL>", "role": "Lead"}], "description": "A curated collection of all country flags in SVG — plus the CSS for easier integration.", "homepage": "https://flagicons.lipis.dev", "keywords": ["country", "country-flags", "css", "icon-css", "svg"], "support": {"issues": "https://github.com/lipis/flag-icons/issues", "source": "https://github.com/lipis/flag-icons/tree/v7.5.0"}, "time": "2025-05-29T17:52:20+00:00"}, {"name": "composer/pcre", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"phpstan/phpstan": "<1.11.10"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-strict-rules": "^1 || ^2", "phpunit/phpunit": "^8 || ^9"}, "type": "library", "extra": {"phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.3.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-11-12T16:29:46+00:00"}, {"name": "cornernote/yii2-workflow-manager", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/cornernote/yii2-workflow-manager.git", "reference": "79dd9bc753b6d202ffc48cd2a967cbefd4cd377e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cornernote/yii2-workflow-manager/zipball/79dd9bc753b6d202ffc48cd2a967cbefd4cd377e", "reference": "79dd9bc753b6d202ffc48cd2a967cbefd4cd377e", "shasum": ""}, "require": {"raoul2000/yii2-workflow": "@dev", "raoul2000/yii2-workflow-view": "@dev", "yiisoft/yii2": "*", "yiisoft/yii2-jui": "~2.0.0"}, "require-dev": {"phpunit/phpunit": "4.*", "scrutinizer/ocular": "~1.1"}, "type": "yii2-extension", "autoload": {"psr-4": {"cornernote\\workflow\\manager\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://mrphp.com.au/"}], "description": "Workflow Manager for Yii2.", "keywords": ["workflow", "yii2"], "support": {"issues": "https://github.com/cornernote/yii2-workflow-manager/issues", "source": "https://github.com/cornernote/yii2-workflow-manager/tree/1.0.2"}, "time": "2018-03-26T03:11:41+00:00"}, {"name": "doctrine/lexer", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/3.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:56:58+00:00"}, {"name": "edofre/yii2-fullcalendar", "version": "V1.0.11", "source": {"type": "git", "url": "https://github.com/Edofre/yii2-fullcalendar.git", "reference": "0eed44a374a95849093891f08df20b79942a0c62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Edofre/yii2-fullcalendar/zipball/0eed44a374a95849093891f08df20b79942a0c62", "reference": "0eed44a374a95849093891f08df20b79942a0c62", "shasum": ""}, "require": {"npm-asset/fullcalendar": "v3.8.0", "php": ">=5.5.0", "yiisoft/yii2": ">=2.0.9", "yiisoft/yii2-jui": ">=2.0.6"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"edofre\\fullcalendar\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Yii2 widget for fullcalendar", "homepage": "https://github.com/edofre/yii2-fullcalendar", "keywords": ["calendar", "event", "fullcalendar", "javascript"], "support": {"issues": "https://github.com/Edofre/yii2-fullcalendar/issues", "source": "https://github.com/Edofre/yii2-fullcalendar/tree/v1.0.11"}, "time": "2018-01-04T21:25:17+00:00"}, {"name": "egulias/email-validator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "shasum": ""}, "require": {"doctrine/lexer": "^2.0 || ^3.0", "php": ">=8.1", "symfony/polyfill-intl-idn": "^1.26"}, "require-dev": {"phpunit/phpunit": "^10.2", "vimeo/psalm": "^5.12"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.4"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2025-03-06T22:45:56+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.18.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "cb56001e54359df7ae76dc522d08845dc741621b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/cb56001e54359df7ae76dc522d08845dc741621b", "reference": "cb56001e54359df7ae76dc522d08845dc741621b", "shasum": ""}, "require": {"php": "~5.6.0 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"cerdic/css-tidy": "^1.7 || ^2.0", "simpletest/simpletest": "dev-master"}, "suggest": {"cerdic/css-tidy": "If you want to use the filter 'Filter.ExtractStyleBlocks'.", "ext-bcmath": "Used for unit conversion and imagecrash protection", "ext-iconv": "Converts text to and from non-UTF-8 encodings", "ext-tidy": "Used for pretty-printing HTML"}, "type": "library", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/v4.18.0"}, "time": "2024-11-01T03:51:45+00:00"}, {"name": "facebook/graph-sdk", "version": "5.1.4", "source": {"type": "git", "url": "https://github.com/facebook/php-graph-sdk.git", "reference": "38fd7187a6704d3ab14ded2f3a534ac4ee6f3481"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/facebook/php-graph-sdk/zipball/38fd7187a6704d3ab14ded2f3a534ac4ee6f3481", "reference": "38fd7187a6704d3ab14ded2f3a534ac4ee6f3481", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4.0"}, "require-dev": {"guzzlehttp/guzzle": "~5.0", "mockery/mockery": "~0.8", "phpunit/phpunit": "~4.0"}, "suggest": {"guzzlehttp/guzzle": "Allows for implementation of the Guzzle HTTP client"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"Facebook\\": "src/Facebook/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Facebook Platform"], "authors": [{"name": "Facebook", "homepage": "https://github.com/facebook/facebook-php-sdk-v4/contributors"}], "description": "Facebook SDK for PHP", "homepage": "https://github.com/facebook/facebook-php-sdk-v4", "keywords": ["facebook", "sdk"], "support": {"issues": "https://github.com/facebook/php-graph-sdk/issues", "source": "https://github.com/facebook/php-graph-sdk/tree/5.1.4"}, "abandoned": true, "time": "2016-05-13T17:28:30+00:00"}, {"name": "fortawesome/font-awesome", "version": "5.15.4", "source": {"type": "git", "url": "https://github.com/FortAwesome/Font-Awesome.git", "reference": "7d3d774145ac38663f6d1effc6def0334b68ab7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FortAwesome/Font-Awesome/zipball/7d3d774145ac38663f6d1effc6def0334b68ab7e", "reference": "7d3d774145ac38663f6d1effc6def0334b68ab7e", "shasum": ""}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["CC-BY-4.0", "OFL-1.1", "MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://twitter.com/supercodepoet"}, {"name": "<PERSON>", "homepage": "http://twitter.com/davegandy"}, {"name": "<PERSON>", "homepage": "http://twitter.com/robmadole"}, {"name": "<PERSON><PERSON>", "homepage": "http://twitter.com/sensibleworld"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://twitter.com/gtagliala"}, {"name": "<PERSON>", "homepage": "http://twitter.com/talbs"}, {"name": "<PERSON>", "homepage": "http://twitter.com/mw77"}], "description": "The iconic font, CSS, and SVG framework", "homepage": "https://fontawesome.com", "keywords": ["FontAwesome", "awesome", "bootstrap", "font", "icon", "svg"], "support": {"docs": "http://fontawesome.com/how-to-use", "email": "<EMAIL>", "issues": "https://github.com/FortAwesome/Font-Awesome/issues", "source": "https://github.com/FortAwesome/Font-Awesome"}, "time": "2021-08-04T19:09:22+00:00"}, {"name": "giggsey/libphonenumber-for-php", "version": "8.13.55", "source": {"type": "git", "url": "https://github.com/giggsey/libphonenumber-for-php.git", "reference": "6e28b3d53cf96d7f41c83d9b80b6021ecbd00537"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/libphonenumber-for-php/zipball/6e28b3d53cf96d7f41c83d9b80b6021ecbd00537", "reference": "6e28b3d53cf96d7f41c83d9b80b6021ecbd00537", "shasum": ""}, "require": {"giggsey/locale": "^2.0", "php": "^7.4|^8.0", "symfony/polyfill-mbstring": "^1.17"}, "replace": {"giggsey/libphonenumber-for-php-lite": "self.version"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.64", "pear/pear-core-minimal": "^1.10", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.7", "phing/phing": "^3.0", "php-coveralls/php-coveralls": "^2.0", "phpunit/phpunit": "^9.6", "symfony/console": "^v5.2", "symfony/var-exporter": "^5.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"libphonenumber\\": "src/"}, "exclude-from-classmap": ["/src/data/", "/src/carrier/data/", "/src/geocoding/data/", "/src/timezone/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "PHP Port of Google's libphonenumber", "homepage": "https://github.com/giggsey/libphonenumber-for-php", "keywords": ["geocoding", "geolocation", "libphonenumber", "mobile", "phonenumber", "validation"], "support": {"issues": "https://github.com/giggsey/libphonenumber-for-php/issues", "source": "https://github.com/giggsey/libphonenumber-for-php"}, "time": "2025-02-14T08:14:08+00:00"}, {"name": "giggsey/locale", "version": "2.8.0", "source": {"type": "git", "url": "https://github.com/giggsey/Locale.git", "reference": "1cd8b3ad2d43e04f4c2c6a240495af44780f809b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/Locale/zipball/1cd8b3ad2d43e04f4c2c6a240495af44780f809b", "reference": "1cd8b3ad2d43e04f4c2c6a240495af44780f809b", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"ext-json": "*", "friendsofphp/php-cs-fixer": "^3.66", "pear/pear-core-minimal": "^1.10", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "^2.17.4", "php-coveralls/php-coveralls": "^2.7", "phpunit/phpunit": "^10.5.45", "symfony/console": "^6.4", "symfony/filesystem": "6.4", "symfony/finder": "^6.4", "symfony/process": "^6.4", "symfony/var-exporter": "^6.4"}, "type": "library", "autoload": {"psr-4": {"Giggsey\\Locale\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "Locale functions required by libphonenumber-for-php", "support": {"issues": "https://github.com/giggsey/Locale/issues", "source": "https://github.com/giggsey/Locale/tree/2.8.0"}, "time": "2025-03-20T14:25:27+00:00"}, {"name": "hail812/yii2-adminlte-widgets", "version": "v1.0.5", "source": {"type": "git", "url": "https://github.com/hail812/yii2-adminlte-widgets.git", "reference": "bc942430d7a5f5636f6c492553b5f444bf4a6df6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hail812/yii2-adminlte-widgets/zipball/bc942430d7a5f5636f6c492553b5f444bf4a6df6", "reference": "bc942430d7a5f5636f6c492553b5f444bf4a6df6", "shasum": ""}, "require": {"yiisoft/yii2": "~2.0.0", "yiisoft/yii2-bootstrap4": "~2.0.8"}, "type": "yii2-extension", "autoload": {"psr-4": {"hail812\\adminlte\\widgets\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "hail", "email": "<EMAIL>"}], "description": "yii2 adminlte widgets", "keywords": ["extension", "yii2"], "support": {"issues": "https://github.com/hail812/yii2-adminlte-widgets/issues", "source": "https://github.com/hail812/yii2-adminlte-widgets/tree/v1.0.5"}, "time": "2022-05-25T05:58:29+00:00"}, {"name": "hail812/yii2-adminlte3", "version": "v1.1.9", "source": {"type": "git", "url": "https://github.com/hail812/yii2-adminlte3.git", "reference": "bcdec8f5e2f9caf7f3a34fad8cdfdd5dbc3a9c31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hail812/yii2-adminlte3/zipball/bcdec8f5e2f9caf7f3a34fad8cdfdd5dbc3a9c31", "reference": "bcdec8f5e2f9caf7f3a34fad8cdfdd5dbc3a9c31", "shasum": ""}, "require": {"almasaeed2010/adminlte": "~3.1", "hail812/yii2-adminlte-widgets": "~1.0.2", "php": ">=7.0", "yiisoft/yii2": "~2.0.0", "yiisoft/yii2-bootstrap4": "~2.0.8"}, "type": "yii2-extension", "autoload": {"psr-4": {"hail812\\adminlte3\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "hail", "email": "<EMAIL>"}], "description": "adminlte3 for yii2", "keywords": ["AdminLTE", "extension", "yii2"], "support": {"issues": "https://github.com/hail812/yii2-adminlte3/issues", "source": "https://github.com/hail812/yii2-adminlte3/tree/v1.1.9"}, "time": "2023-10-23T05:58:30+00:00"}, {"name": "kartik-v/bootstrap-checkbox-x", "version": "v1.5.7", "source": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-checkbox-x.git", "reference": "1060db69ad2519a2b36cf5e0e28f542c21135672"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/bootstrap-checkbox-x/zipball/1060db69ad2519a2b36cf5e0e28f542c21135672", "reference": "1060db69ad2519a2b36cf5e0e28f542c21135672", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\checkbox\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An extended checkbox plugin for bootstrap with three states and additional styles.", "homepage": "https://github.com/kartik-v/bootstrap-checkbox-x", "keywords": ["bootstrap", "checkbox", "indeterminate", "j<PERSON>y", "null", "state", "three"], "support": {"issues": "https://github.com/kartik-v/bootstrap-checkbox-x/issues", "source": "https://github.com/kartik-v/bootstrap-checkbox-x/tree/v1.5.7"}, "time": "2021-09-19T12:30:10+00:00"}, {"name": "kartik-v/bootstrap-fileinput", "version": "v5.5.4", "source": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-fileinput.git", "reference": "8de1bed638823c70272b2578847e2b31e42677ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/bootstrap-fileinput/zipball/8de1bed638823c70272b2578847e2b31e42677ba", "reference": "8de1bed638823c70272b2578847e2b31e42677ba", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.5.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\fileinput\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced HTML 5 file input for Bootstrap 5.x, 4.x, and 3.x with features for file preview for many file types, multiple selection, ajax uploads, and more.", "homepage": "https://github.com/kartik-v/bootstrap-fileinput", "keywords": ["ajax", "bootstrap", "delete", "file", "image", "input", "j<PERSON>y", "multiple", "preview", "progress", "upload"], "support": {"issues": "https://github.com/kartik-v/bootstrap-fileinput/issues", "source": "https://github.com/kartik-v/bootstrap-fileinput/tree/v5.5.4"}, "funding": [{"url": "https://opencollective.com/bootstrap-fileinput", "type": "open_collective"}], "time": "2024-04-09T03:30:45+00:00"}, {"name": "kartik-v/bootstrap-popover-x", "version": "v1.5.4", "source": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-popover-x.git", "reference": "879def62529797833e10885b6e77864e83e9e240"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/bootstrap-popover-x/zipball/879def62529797833e10885b6e77864e83e9e240", "reference": "879def62529797833e10885b6e77864e83e9e240", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\popover\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Bootstrap Popover Extended - Popover with modal behavior, styling enhancements and more.", "homepage": "https://github.com/kartik-v/bootstrap-popover-x", "keywords": ["bootstrap", "extended", "j<PERSON>y", "modal", "modal-popover", "popover", "popover-x"], "support": {"issues": "https://github.com/kartik-v/bootstrap-popover-x/issues", "source": "https://github.com/kartik-v/bootstrap-popover-x/tree/v1.5.4"}, "funding": [{"url": "https://opencollective.com/bootstrap-popover-x", "type": "open_collective"}], "time": "2024-03-12T13:23:15+00:00"}, {"name": "kartik-v/bootstrap-star-rating", "version": "v4.1.2", "source": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-star-rating.git", "reference": "c301efed4c82e9d5f11a0845ae428ba60931b44e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/bootstrap-star-rating/zipball/c301efed4c82e9d5f11a0845ae428ba60931b44e", "reference": "c301efed4c82e9d5f11a0845ae428ba60931b44e", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A simple yet powerful JQuery star rating plugin for Bootstrap.", "homepage": "https://github.com/kartik-v/bootstrap-star-rating", "keywords": ["Rating", "awesome", "bootstrap", "font", "glyphicon", "star", "svg"], "support": {"issues": "https://github.com/kartik-v/bootstrap-star-rating/issues", "source": "https://github.com/kartik-v/bootstrap-star-rating/tree/v4.1.2"}, "funding": [{"url": "https://opencollective.com/bootstrap-star-rating", "type": "open_collective"}], "time": "2021-09-20T03:06:01+00:00"}, {"name": "kartik-v/bootstrap-tabs-x", "version": "v1.3.5", "source": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-tabs-x.git", "reference": "12e5eeceda5ecbdf1bc7e429c4cea69c0e0b5267"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/bootstrap-tabs-x/zipball/12e5eeceda5ecbdf1bc7e429c4cea69c0e0b5267", "reference": "12e5eeceda5ecbdf1bc7e429c4cea69c0e0b5267", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\tabs\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Extended Bootstrap Tabs with ability to align tabs in multiple ways, add borders, rotated titles, and more.", "homepage": "https://github.com/kartik-v/bootstrap-tabs-x", "keywords": ["bootstrap", "extended", "j<PERSON>y", "modal", "modal-tabs", "tabs", "tabs-x"], "support": {"issues": "https://github.com/kartik-v/bootstrap-tabs-x/issues", "source": "https://github.com/kartik-v/bootstrap-tabs-x/tree/v1.3.5"}, "time": "2021-09-21T02:01:51+00:00"}, {"name": "kartik-v/dependent-dropdown", "version": "v1.4.9", "source": {"type": "git", "url": "https://github.com/kartik-v/dependent-dropdown.git", "reference": "54a8806002ee21b744508a2edb95ed01d35c6cf9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/dependent-dropdown/zipball/54a8806002ee21b744508a2edb95ed01d35c6cf9", "reference": "54a8806002ee21b744508a2edb95ed01d35c6cf9", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\depdrop\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A multi level dependent dropdown JQuery plugin that allows nested dependencies.", "homepage": "https://github.com/kartik-v/dependent-dropdown", "keywords": ["dependent", "dropdown", "j<PERSON>y", "option", "select"], "support": {"issues": "https://github.com/kartik-v/dependent-dropdown/issues", "source": "https://github.com/kartik-v/dependent-dropdown/tree/master"}, "time": "2019-03-09T10:53:11+00:00"}, {"name": "kartik-v/php-date-formatter", "version": "v1.3.7", "source": {"type": "git", "url": "https://github.com/kartik-v/php-date-formatter.git", "reference": "4b47950027414fc8064af228eb1e63cd8c9b04f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/php-date-formatter/zipball/4b47950027414fc8064af228eb1e63cd8c9b04f9", "reference": "4b47950027414fc8064af228eb1e63cd8c9b04f9", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\dateformatter\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A Javascript datetime formatting and manipulation library using PHP date-time formats.", "homepage": "https://github.com/kartik-v/php-date-formatter", "keywords": ["date", "datetime", "formatter", "javascript", "php", "php-date-formatter.js", "time"], "support": {"issues": "https://github.com/kartik-v/php-date-formatter/issues", "source": "https://github.com/kartik-v/php-date-formatter/tree/v1.3.7"}, "time": "2025-02-06T20:47:58+00:00"}, {"name": "kartik-v/strength-meter", "version": "v1.1.4", "source": {"type": "git", "url": "https://github.com/kartik-v/strength-meter.git", "reference": "100147f588b12ff819014b445007a0decfd95fbb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/strength-meter/zipball/100147f588b12ff819014b445007a0decfd95fbb", "reference": "100147f588b12ff819014b445007a0decfd95fbb", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\strengthmeter\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A dynamic strength meter for password input validation with various configurable options.", "homepage": "https://github.com/kartik-v/strength-meter", "keywords": ["bootstrap", "j<PERSON>y", "meter", "password", "strength"], "support": {"issues": "https://github.com/kartik-v/strength-meter/issues", "source": "https://github.com/kartik-v/strength-meter/tree/master"}, "time": "2018-01-20T17:52:01+00:00"}, {"name": "kartik-v/yii2-bootstrap5-dropdown", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-bootstrap5-dropdown.git", "reference": "459e4c06c64e49f89ac372ce7c2cabb961f56ec9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-bootstrap5-dropdown/zipball/459e4c06c64e49f89ac372ce7c2cabb961f56ec9", "reference": "459e4c06c64e49f89ac372ce7c2cabb961f56ec9", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0"}, "suggest": {"yiisoft/yii2-bootstrap5": "The Yii 2 Bootstrap 4 extension must be installed separately for this extension to work."}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\bs5dropdown\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Bootstrap 4 dropdown widget for Yii2 with nested submenu support", "homepage": "https://github.com/kartik-v/yii2-bootstrap5-dropdown", "keywords": ["bootstrap", "dropdown", "j<PERSON>y", "nested", "submenu", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-bootstrap5-dropdown/issues", "source": "https://github.com/kartik-v/yii2-bootstrap5-dropdown/tree/v1.0.2"}, "time": "2022-01-10T04:05:31+00:00"}, {"name": "kartik-v/yii2-builder", "version": "v1.6.9", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-builder.git", "reference": "616c2f8d322a45c037a4d32f0ee1eb1457865e73"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-builder/zipball/616c2f8d322a45c037a4d32f0ee1eb1457865e73", "reference": "616c2f8d322a45c037a4d32f0ee1eb1457865e73", "shasum": ""}, "require": {"kartik-v/yii2-grid": ">=3.2.5", "kartik-v/yii2-helpers": ">=1.3.8", "kartik-v/yii2-widget-activeform": ">=1.6.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-4": {"kartik\\builder\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Build forms (single-row or multi-row/tabular) easily for Yii Framework 2.0", "homepage": "https://github.com/kartik-v/yii2-builder", "keywords": ["builder", "extension", "form", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-builder/issues", "source": "https://github.com/kartik-v/yii2-builder/tree/v1.6.9"}, "funding": [{"url": "https://opencollective.com/kartik-v", "type": "open_collective"}], "time": "2022-01-05T14:09:44+00:00"}, {"name": "kartik-v/yii2-checkbox-x", "version": "v1.0.7", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-checkbox-x.git", "reference": "0ac0c009d8f6ba2f9f31d0db12a02581c1e698b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-checkbox-x/zipball/0ac0c009d8f6ba2f9f31d0db12a02581c1e698b4", "reference": "0ac0c009d8f6ba2f9f31d0db12a02581c1e698b4", "shasum": ""}, "require": {"kartik-v/bootstrap-checkbox-x": "~1.5", "kartik-v/yii2-krajee-base": ">=3.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\checkbox\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Extended checkbox widget for bootstrap with three states and additional styles.", "homepage": "https://github.com/kartik-v/yii2-checkbox-x", "keywords": ["bootstrap", "checkbox", "indeterminate", "j<PERSON>y", "null", "state", "three"], "support": {"issues": "https://github.com/kartik-v/yii2-checkbox-x/issues", "source": "https://github.com/kartik-v/yii2-checkbox-x/tree/v1.0.7"}, "time": "2022-01-10T04:14:49+00:00"}, {"name": "kartik-v/yii2-context-menu", "version": "v1.2.4", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-context-menu.git", "reference": "a2227e7bc2091ea0bde1c2d812c4a5341cf3009d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-context-menu/zipball/a2227e7bc2091ea0bde1c2d812c4a5341cf3009d", "reference": "a2227e7bc2091ea0bde1c2d812c4a5341cf3009d", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=3.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"kartik\\cmenu\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A context menu extension for Yii framework 2.0", "homepage": "https://github.com/kartik-v/yii2-context-menu", "keywords": ["Context", "click", "extension", "menu", "mouse", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-context-menu/issues", "source": "https://github.com/kartik-v/yii2-context-menu/tree/v1.2.4"}, "time": "2021-09-01T12:02:51+00:00"}, {"name": "kartik-v/yii2-date-range", "version": "v1.7.3", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-date-range.git", "reference": "7d80d6e2598e252487fe1acf911b8cb43f45e3b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-date-range/zipball/7d80d6e2598e252487fe1acf911b8cb43f45e3b1", "reference": "7d80d6e2598e252487fe1acf911b8cb43f45e3b1", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=3.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.7.x-dev"}}, "autoload": {"psr-4": {"kartik\\daterange\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An advanced Yii 2 date range picker input for based on bootstrap-daterangepicker plugin.", "homepage": "https://github.com/kartik-v/yii2-date-range", "keywords": ["bootstrap", "bootstrap 3", "date", "date-range", "extension", "range", "time", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-date-range/issues", "source": "https://github.com/kartik-v/yii2-date-range/tree/v1.7.3"}, "time": "2021-09-01T12:16:39+00:00"}, {"name": "kartik-v/yii2-datecontrol", "version": "v1.9.9", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-datecontrol.git", "reference": "88ac2401ce1858b0da98b498197373691953c3d6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-datecontrol/zipball/88ac2401ce1858b0da98b498197373691953c3d6", "reference": "88ac2401ce1858b0da98b498197373691953c3d6", "shasum": ""}, "require": {"kartik-v/php-date-formatter": ">1.3", "kartik-v/yii2-krajee-base": ">=3.0.4"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}, "autoload": {"psr-4": {"kartik\\datecontrol\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Date control module allowing separation of formats for View and Model for Yii Framework 2.0", "homepage": "https://github.com/kartik-v/yii2-datecontrol", "keywords": ["control", "date", "extension", "format", "yii", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-datecontrol/issues", "source": "https://github.com/kartik-v/yii2-datecontrol/tree/v1.9.9"}, "time": "2022-03-04T09:47:47+00:00"}, {"name": "kartik-v/yii2-detail-view", "version": "v1.8.7", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-detail-view.git", "reference": "0b130581bce1ff26a98750433489cdc4816080a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-detail-view/zipball/0b130581bce1ff26a98750433489cdc4816080a1", "reference": "0b130581bce1ff26a98750433489cdc4816080a1", "shasum": ""}, "require": {"kartik-v/yii2-dialog": "~1.0", "kartik-v/yii2-krajee-base": ">=3.0.4", "kartik-v/yii2-widget-activeform": ">=1.6.2"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.8.x-dev"}}, "autoload": {"psr-4": {"kartik\\detail\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii 2 Detail View widget with special Bootstrap styles, ability to edit data, and more.", "homepage": "https://github.com/kartik-v/yii2-detail-view", "keywords": ["detail", "detail view", "extension", "form", "grid", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-detail-view/issues", "source": "https://github.com/kartik-v/yii2-detail-view/tree/v1.8.7"}, "time": "2022-03-04T09:42:29+00:00"}, {"name": "kartik-v/yii2-dialog", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-dialog.git", "reference": "510c3a35ffe79987cde9a9366cedbff545fd92d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-dialog/zipball/510c3a35ffe79987cde9a9366cedbff545fd92d4", "reference": "510c3a35ffe79987cde9a9366cedbff545fd92d4", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=3.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\dialog\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An asset bundle for bootstrap3-dialog for Yii 2.0 framework.", "homepage": "https://github.com/kartik-v/yii2-dialog", "keywords": ["alert", "bootstrap", "dialog", "extension", "modal", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-dialog/issues", "source": "https://github.com/kartik-v/yii2-dialog/tree/v1.0.6"}, "time": "2021-09-02T08:26:37+00:00"}, {"name": "kartik-v/yii2-dropdown-x", "version": "v1.2.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-dropdown-x.git", "reference": "87de704bd944b2226e5e0b36ec73f06d72da110f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-dropdown-x/zipball/87de704bd944b2226e5e0b36ec73f06d72da110f", "reference": "87de704bd944b2226e5e0b36ec73f06d72da110f", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"kartik\\dropdown\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An extended bootstrap 3.x dropdown widget for Yii 2 with submenu drilldown.", "homepage": "https://github.com/kartik-v/yii2-dropdown-x", "keywords": ["Context", "click", "extension", "menu", "mouse", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-dropdown-x/issues", "source": "https://github.com/kartik-v/yii2-dropdown-x/tree/v1.2.1"}, "time": "2022-01-11T04:38:44+00:00"}, {"name": "kartik-v/yii2-dynagrid", "version": "v1.5.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-dynagrid.git", "reference": "4d81fda0775a2994df3d863b1259dc5edf1e8952"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-dynagrid/zipball/4d81fda0775a2994df3d863b1259dc5edf1e8952", "reference": "4d81fda0775a2994df3d863b1259dc5edf1e8952", "shasum": ""}, "require": {"kartik-v/yii2-grid": ">= 3.5.2", "kartik-v/yii2-krajee-base": ">= 3.0.4", "kartik-v/yii2-sortable": "~1.2", "kartik-v/yii2-widget-activeform": ">= 1.6.2", "kartik-v/yii2-widget-select2": ">= 2.2.3"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"kartik\\dynagrid\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Turbo charge the Yii 2 GridView with personalized columns, page size, and themes.", "homepage": "https://github.com/kartik-v/yii2-dynagrid", "keywords": ["columns", "dynamic", "extension", "grid", "hide", "order", "reorder", "show", "sort", "visibility", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-dynagrid/issues", "source": "https://github.com/kartik-v/yii2-dynagrid/tree/v1.5.5"}, "time": "2023-07-25T08:41:55+00:00"}, {"name": "kartik-v/yii2-editable", "version": "v1.8.0", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-editable.git", "reference": "ae4cc2384e667ba9dfe8bfb0098716caeb2500a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-editable/zipball/ae4cc2384e667ba9dfe8bfb0098716caeb2500a8", "reference": "ae4cc2384e667ba9dfe8bfb0098716caeb2500a8", "shasum": ""}, "require": {"kartik-v/yii2-popover-x": "~1.3", "kartik-v/yii2-widget-activeform": ">=1.6.2"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.8.x-dev"}}, "autoload": {"psr-4": {"kartik\\editable\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced editable widget for Yii 2.0 that allows easy editing of displayed data with numerous configuration possibilities.", "homepage": "https://github.com/kartik-v/yii2-editable", "keywords": ["bootstrap", "editable", "input", "j<PERSON>y", "popover", "popover-x", "widget"], "support": {"issues": "https://github.com/kartik-v/yii2-editable/issues", "source": "https://github.com/kartik-v/yii2-editable/tree/v1.8.0"}, "time": "2022-04-29T12:51:01+00:00"}, {"name": "kartik-v/yii2-export", "version": "v1.4.3", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-export.git", "reference": "5c033d407631f8f4aa1f47e4a53e0204d45bb29c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-export/zipball/5c033d407631f8f4aa1f47e4a53e0204d45bb29c", "reference": "5c033d407631f8f4aa1f47e4a53e0204d45bb29c", "shasum": ""}, "require": {"kartik-v/yii2-dynagrid": ">=1.5.5", "kartik-v/yii2-mpdf": ">=1.0", "phpoffice/phpspreadsheet": ">=1.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"kartik\\export\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A library to export server/db data in various formats (e.g. excel, html, pdf, csv etc.)", "homepage": "https://github.com/kartik-v/yii2-export", "keywords": ["OpenXML", "csv", "export", "extension", "html", "json", "pdf", "spreadsheet", "text", "widget", "xls", "xlsx", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-export/issues", "source": "https://github.com/kartik-v/yii2-export/tree/v1.4.3"}, "funding": [{"url": "https://opencollective.com/yii2-export", "type": "open_collective"}], "time": "2023-07-25T11:05:33+00:00"}, {"name": "kartik-v/yii2-field-range", "version": "v1.3.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-field-range.git", "reference": "8a18edb343b3beb96ddc86e4a06aee28be160787"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-field-range/zipball/8a18edb343b3beb96ddc86e4a06aee28be160787", "reference": "8a18edb343b3beb96ddc86e4a06aee28be160787", "shasum": ""}, "require": {"kartik-v/yii2-helpers": ">=1.3.9", "kartik-v/yii2-widget-activeform": ">=1.5.7"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"kartik\\field\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Easily manage Yii 2 ActiveField ranges (from/to) with Bootstrap 3 addons markup and more", "homepage": "https://github.com/kartik-v/yii2-field-range", "keywords": ["addon", "bootstrap", "bootstrap 3", "bootstrap 4", "date", "extension", "field-range", "from", "range", "to", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-field-range/issues", "source": "https://github.com/kartik-v/yii2-field-range/tree/v1.3.5"}, "time": "2019-05-25T07:21:55+00:00"}, {"name": "kartik-v/yii2-grid", "version": "v3.5.3", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-grid.git", "reference": "cfc1a8e18bddfe22668783abb70f08583abab0a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-grid/zipball/cfc1a8e18bddfe22668783abb70f08583abab0a9", "reference": "cfc1a8e18bddfe22668783abb70f08583abab0a9", "shasum": ""}, "require": {"kartik-v/yii2-dialog": "~1.0", "kartik-v/yii2-krajee-base": ">=3.0.3"}, "suggest": {"kartik-v/yii2-bootstrap4-dropdown": "For enabling dropdown support when using with Bootstrap v4.x", "kartik-v/yii2-bootstrap5-dropdown": "For enabling dropdown support when using with Bootstrap v5.x", "kartik-v/yii2-mpdf": "For exporting grids to PDF"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "3.5.x-dev"}}, "autoload": {"psr-4": {"kartik\\grid\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Yii 2 GridView on steroids. Various enhancements and utilities for the Yii 2.0 GridView widget.", "homepage": "https://github.com/kartik-v/yii2-grid", "keywords": ["extension", "grid", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-grid/issues", "source": "https://github.com/kartik-v/yii2-grid/tree/v3.5.3"}, "funding": [{"url": "https://opencollective.com/yii2-grid", "type": "open_collective"}], "time": "2023-07-25T11:41:33+00:00"}, {"name": "kartik-v/yii2-helpers", "version": "v1.3.9", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-helpers.git", "reference": "0bec7a97bf9d0293d96be5c496fe9654c4dec94b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-helpers/zipball/0bec7a97bf9d0293d96be5c496fe9654c4dec94b", "reference": "0bec7a97bf9d0293d96be5c496fe9654c4dec94b", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"kartik\\helpers\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A collection of useful helper functions for Yii Framework 2.0", "homepage": "https://github.com/kartik-v/yii2-helpers", "keywords": ["bootstrap", "extension", "helper", "utilities", "yii", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-helpers/issues", "source": "https://github.com/kartik-v/yii2-helpers/tree/master"}, "time": "2018-10-09T08:03:44+00:00"}, {"name": "kartik-v/yii2-icons", "version": "v1.4.8", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-icons.git", "reference": "81334b3d888d4baaeb6ac458475258130474237e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-icons/zipball/81334b3d888d4baaeb6ac458475258130474237e", "reference": "81334b3d888d4baaeb6ac458475258130474237e", "shasum": ""}, "require": {"components/flag-icon-css": "*", "kartik-v/yii2-krajee-base": ">=3.0.4", "yiisoft/yii2-jui": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"kartik\\icons\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://demos.krajee.com/"}], "description": "Set of icon frameworks for use in Yii Framework 2.0", "homepage": "https://github.com/kartik-v/yii2-icons", "keywords": ["extension", "font", "icon", "yii", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-icons/issues", "source": "https://github.com/kartik-v/yii2-icons/tree/v1.4.8"}, "time": "2022-03-04T10:34:44+00:00"}, {"name": "kartik-v/yii2-ipinfo", "version": "v2.0.2", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-ipinfo.git", "reference": "f0fda7ab9372ef238977d69006ec6b56c56a2c8d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-ipinfo/zipball/f0fda7ab9372ef238977d69006ec6b56c56a2c8d", "reference": "f0fda7ab9372ef238977d69006ec6b56c56a2c8d", "shasum": ""}, "require": {"kartik-v/yii2-icons": "~1.4", "kartik-v/yii2-popover-x": "~1.3", "yiisoft/yii2-httpclient": "~2.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\ipinfo\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An IP address information display widget for Yii 2.0 with country flag and geo position info.", "homepage": "https://github.com/kartik-v/yii2-ipinfo", "keywords": ["IP", "country", "extension", "flag", "geo", "ipinfo", "location", "module", "position", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-ipinfo/issues", "source": "https://github.com/kartik-v/yii2-ipinfo/tree/master"}, "time": "2019-10-07T15:07:24+00:00"}, {"name": "kartik-v/yii2-krajee-base", "version": "v3.0.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-krajee-base.git", "reference": "5c095126d1be47e0bb1f92779b7dc099f6feae31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-krajee-base/zipball/5c095126d1be47e0bb1f92779b7dc099f6feae31", "reference": "5c095126d1be47e0bb1f92779b7dc099f6feae31", "shasum": ""}, "suggest": {"yiisoft/yii2-bootstrap": "for Krajee extensions to work with Bootstrap 3.x version", "yiisoft/yii2-bootstrap4": "for Krajee extensions to work with Bootstrap 4.x version", "yiisoft/yii2-bootstrap5": "for Krajee extensions to work with Bootstrap 5.x version"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\base\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Base library and foundation components for all Yii2 Krajee extensions.", "homepage": "https://github.com/kartik-v/yii2-krajee-base", "keywords": ["base", "extension", "foundation", "krajee", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-krajee-base/issues", "source": "https://github.com/kartik-v/yii2-krajee-base/tree/v3.0.5"}, "time": "2022-06-01T14:05:39+00:00"}, {"name": "kartik-v/yii2-label-inplace", "version": "v1.2.3", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-label-inplace.git", "reference": "8a152fe1a55b87b6ac69624e4da0c1c8a828c172"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-label-inplace/zipball/8a152fe1a55b87b6ac69624e4da0c1c8a828c172", "reference": "8a152fe1a55b87b6ac69624e4da0c1c8a828c172", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"kartik\\label\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A form enhancement widget for Yii2 allowing in-field label support.", "homepage": "https://github.com/kartik-v/yii2-label-inplace", "keywords": ["extension", "form", "inline", "inplace", "input", "label", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-label-inplace/issues", "source": "https://github.com/kartik-v/yii2-label-inplace/tree/master"}, "time": "2018-09-16T17:57:12+00:00"}, {"name": "kartik-v/yii2-markdown", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-markdown.git", "reference": "c608d88d417423f0a89a0a8259777dd5d7107b2b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-markdown/zipball/c608d88d417423f0a89a0a8259777dd5d7107b2b", "reference": "c608d88d417423f0a89a0a8259777dd5d7107b2b", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=1.9", "michelf/php-markdown": "~1.8", "michelf/php-smartypants": "~1.8"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"kartik\\markdown\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Advanced Markdown editing and conversion utilities for Yii Framework 2.0", "homepage": "https://github.com/kartik-v/yii2-markdown", "keywords": ["bootstrap", "converter", "editor", "extension", "form", "input", "j<PERSON>y", "markdown", "yii", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-markdown/issues", "source": "https://github.com/kartik-v/yii2-markdown/tree/master"}, "time": "2018-09-21T10:57:01+00:00"}, {"name": "kartik-v/yii2-money", "version": "v1.2.3", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-money.git", "reference": "e656608dd32cc3aaade99af7c920362e00a42dbe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-money/zipball/e656608dd32cc3aaade99af7c920362e00a42dbe", "reference": "e656608dd32cc3aaade99af7c920362e00a42dbe", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"kartik\\money\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An advanced money mask input for Yii 2.0 based on jquery maskmoney plugin styled for Bootstrap 3.", "homepage": "https://github.com/kartik-v/yii2-money", "keywords": ["bootstrap", "bootstrap 3", "currency", "extension", "format", "j<PERSON>y", "mask", "money", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-money/issues", "source": "https://github.com/kartik-v/yii2-money/tree/master"}, "time": "2018-10-09T12:17:48+00:00"}, {"name": "kartik-v/yii2-mpdf", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-mpdf.git", "reference": "7ea3a5f9bcf5bcf6d681bd82cbe843c356d1428c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-mpdf/zipball/7ea3a5f9bcf5bcf6d681bd82cbe843c356d1428c", "reference": "7ea3a5f9bcf5bcf6d681bd82cbe843c356d1428c", "shasum": ""}, "require": {"mpdf/mpdf": "~8.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\mpdf\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A Yii2 wrapper component for the mPDF library which generates PDF files from UTF-8 encoded HTML.", "homepage": "https://github.com/kartik-v/yii2-mpdf", "keywords": ["component", "extension", "html", "mpdf", "pdf", "utf8", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-mpdf/issues", "source": "https://github.com/kartik-v/yii2-mpdf/tree/master"}, "funding": [{"url": "https://opencollective.com/yii2-mpdf", "type": "open_collective"}], "time": "2020-04-14T08:55:18+00:00"}, {"name": "kartik-v/yii2-nav-x", "version": "v1.2.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-nav-x.git", "reference": "7ee28d76f7a82c7108a1c9c11fbeb5a0d67b64cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-nav-x/zipball/7ee28d76f7a82c7108a1c9c11fbeb5a0d67b64cf", "reference": "7ee28d76f7a82c7108a1c9c11fbeb5a0d67b64cf", "shasum": ""}, "require": {"kartik-v/yii2-dropdown-x": "~1.2", "kartik-v/yii2-krajee-base": ">=3.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"kartik\\nav\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An extended bootstrap Nav widget for Yii 2 with submenu drilldown.", "homepage": "https://github.com/kartik-v/yii2-nav-x", "keywords": ["Context", "click", "extension", "menu", "mouse", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-nav-x/issues", "source": "https://github.com/kartik-v/yii2-nav-x/tree/v1.2.5"}, "time": "2021-11-02T12:38:43+00:00"}, {"name": "kartik-v/yii2-password", "version": "v1.5.7", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-password.git", "reference": "59910c89c5e21f231dc2947b412196d22bcd47e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-password/zipball/59910c89c5e21f231dc2947b412196d22bcd47e2", "reference": "59910c89c5e21f231dc2947b412196d22bcd47e2", "shasum": ""}, "require": {"kartik-v/strength-meter": "~1.1", "kartik-v/yii2-krajee-base": ">=3.0.4"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"kartik\\password\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Useful password strength validation utilities for Yii Framework 2.0", "homepage": "https://github.com/kartik-v/yii2-password", "keywords": ["auth", "bootstrap", "extension", "form", "input", "j<PERSON>y", "password", "strength", "yii", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-password/issues", "source": "https://github.com/kartik-v/yii2-password/tree/v1.5.7"}, "time": "2022-05-16T05:38:31+00:00"}, {"name": "kartik-v/yii2-popover-x", "version": "v1.3.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-popover-x.git", "reference": "b0320d1315bbfce31ec8907882c6f4abed223a28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-popover-x/zipball/b0320d1315bbfce31ec8907882c6f4abed223a28", "reference": "b0320d1315bbfce31ec8907882c6f4abed223a28", "shasum": ""}, "require": {"kartik-v/bootstrap-popover-x": ">=1.4", "kartik-v/yii2-krajee-base": ">=2.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"kartik\\popover\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An extended bootstrap 3.0 popover widget which combines both the bootstrap popover and modal features and includes various new styling enhancements.", "homepage": "https://github.com/kartik-v/yii2-popover-x", "keywords": ["bootstrap", "extended", "j<PERSON>y", "modal", "modal-popover", "popover", "popover-x"], "support": {"issues": "https://github.com/kartik-v/yii2-popover-x/issues", "source": "https://github.com/kartik-v/yii2-popover-x/tree/master"}, "time": "2020-04-02T17:20:29+00:00"}, {"name": "kartik-v/yii2-slider", "version": "v1.3.2", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-slider.git", "reference": "81a2463294fa88a58d0ed8214254c8a6b63b7e89"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-slider/zipball/81a2463294fa88a58d0ed8214254c8a6b63b7e89", "reference": "81a2463294fa88a58d0ed8214254c8a6b63b7e89", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\slider\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An advanced slider input for Yii Framework 2 for both touch enabled and desktop devices based on bootstrap-slider.", "homepage": "https://github.com/kartik-v/yii2-slider", "keywords": ["bootstrap", "bootstrap 3", "extension", "range", "slider", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-slider/issues", "source": "https://github.com/kartik-v/yii2-slider/tree/master"}, "time": "2016-01-10T17:24:24+00:00"}, {"name": "kartik-v/yii2-social", "version": "v1.3.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-social.git", "reference": "95409020b2cdfb33560482ec4825dbbb97056368"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-social/zipball/95409020b2cdfb33560482ec4825dbbb97056368", "reference": "95409020b2cdfb33560482ec4825dbbb97056368", "shasum": ""}, "require": {"facebook/graph-sdk": "~5.0", "kartik-v/yii2-krajee-base": ">=1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"kartik\\social\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Module containing useful widgets for Yii Framework 2.0 that integrates social functionalities from DISQUS, Facebook, Google etc.", "homepage": "https://github.com/kartik-v/yii2-social", "keywords": ["disqus", "extension", "facebook", "social", "twitter", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-social/issues", "source": "https://github.com/kartik-v/yii2-social/tree/master"}, "time": "2018-09-21T09:47:37+00:00"}, {"name": "kartik-v/yii2-sortable", "version": "v1.2.2", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-sortable.git", "reference": "a6f9a9adb873d6345de92bbe5e436ac1a1db30bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-sortable/zipball/a6f9a9adb873d6345de92bbe5e436ac1a1db30bc", "reference": "a6f9a9adb873d6345de92bbe5e436ac1a1db30bc", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"kartik\\sortable\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Create sortable lists and grids using HTML5 drag and drop API for Yii 2.0.", "homepage": "https://github.com/kartik-v/yii2-sortable", "keywords": ["bootstrap", "extension", "j<PERSON>y", "range", "sortable", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-sortable/issues", "source": "https://github.com/kartik-v/yii2-sortable/tree/master"}, "time": "2018-10-09T13:25:05+00:00"}, {"name": "kartik-v/yii2-sortable-input", "version": "v1.2.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-sortable-input.git", "reference": "88fa29f63a32ceb25a597e92f9da37deae7e15c2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-sortable-input/zipball/88fa29f63a32ceb25a597e92f9da37deae7e15c2", "reference": "88fa29f63a32ceb25a597e92f9da37deae7e15c2", "shasum": ""}, "require": {"kartik-v/yii2-sortable": "~1.2"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"kartik\\sortinput\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Sortable input widget based on yii2-sortable extension.", "homepage": "https://github.com/kartik-v/yii2-sortable-input", "keywords": ["bootstrap", "extension", "input", "j<PERSON>y", "range", "sortable", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-sortable-input/issues", "source": "https://github.com/kartik-v/yii2-sortable-input/tree/master"}, "time": "2018-08-29T02:33:59+00:00"}, {"name": "kartik-v/yii2-tabs-x", "version": "v1.2.9", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-tabs-x.git", "reference": "20e2a2b41ca43e09574caab408004e5ac4e00a7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-tabs-x/zipball/20e2a2b41ca43e09574caab408004e5ac4e00a7d", "reference": "20e2a2b41ca43e09574caab408004e5ac4e00a7d", "shasum": ""}, "require": {"kartik-v/bootstrap-tabs-x": "~1.3", "kartik-v/yii2-krajee-base": ">=3.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"kartik\\tabs\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A supercharged Bootstrap tabs widget with ability to align tabs in multiple ways, add borders, rotated titles, and more.", "homepage": "https://github.com/kartik-v/yii2-tabs-x", "keywords": ["bootstrap", "extended", "j<PERSON>y", "modal", "modal-tabs", "tabs", "tabs-x"], "support": {"issues": "https://github.com/kartik-v/yii2-tabs-x/issues", "source": "https://github.com/kartik-v/yii2-tabs-x/tree/v1.2.9"}, "time": "2022-10-15T11:14:01+00:00"}, {"name": "kartik-v/yii2-validators", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-validators.git", "reference": "756b4461d9409276c43f9574ceda597cab44e65b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-validators/zipball/756b4461d9409276c43f9574ceda597cab44e65b", "reference": "756b4461d9409276c43f9574ceda597cab44e65b", "shasum": ""}, "require": {"giggsey/libphonenumber-for-php": "^8", "kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"bootstrap": "kartik\\validators\\Bootstrap", "branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\validators\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 model validator components / utilities for Yii2 Framework", "homepage": "https://github.com/kartik-v/yii2-email-validator", "keywords": ["email", "j<PERSON>y", "krajee", "model", "multiple", "validation", "validator", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-validators/issues", "source": "https://github.com/kartik-v/yii2-validators/tree/v1.0.3"}, "time": "2019-08-22T12:32:31+00:00"}, {"name": "kartik-v/yii2-widget-activeform", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-activeform.git", "reference": "697407c8fa9c81593a7bb9bef4b7ad53f7d38b79"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-activeform/zipball/697407c8fa9c81593a7bb9bef4b7ad53f7d38b79", "reference": "697407c8fa9c81593a7bb9bef4b7ad53f7d38b79", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=3.0.3"}, "default-branch": true, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-4": {"kartik\\form\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 active-form and active-field with full bootstrap styling support (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-activeform", "keywords": ["activefield", "activeform", "extension", "field", "form", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-activeform/issues", "source": "https://github.com/kartik-v/yii2-widget-activeform/tree/v1.6.4"}, "funding": [{"url": "https://opencollective.com/yii2-widgets", "type": "open_collective"}], "time": "2023-07-31T11:33:59+00:00"}, {"name": "kartik-v/yii2-widget-affix", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-affix.git", "reference": "2184119bfa518c285406156f744769b13b861712"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-affix/zipball/2184119bfa518c285406156f744769b13b861712", "reference": "2184119bfa518c285406156f744769b13b861712", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\affix\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A scrollspy and affixed enhanced navigation to highlight page sections (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-affix", "keywords": ["affix", "bootstrap", "extension", "j<PERSON>y", "navigation", "plugin", "scrollspy", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-affix/issues", "source": "https://github.com/kartik-v/yii2-widget-affix/tree/master"}, "time": "2014-11-09T04:56:27+00:00"}, {"name": "kartik-v/yii2-widget-alert", "version": "v1.1.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-alert.git", "reference": "6a45d7dc294eecd578cf8cb9acb671d1cafa0727"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-alert/zipball/6a45d7dc294eecd578cf8cb9acb671d1cafa0727", "reference": "6a45d7dc294eecd578cf8cb9acb671d1cafa0727", "shasum": ""}, "require": {"kartik-v/yii2-widget-growl": ">=1.1.2"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"kartik\\alert\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A widget to generate alert based notifications using bootstrap-alert plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-alert", "keywords": ["alert", "block", "bootstrap", "extension", "flash", "j<PERSON>y", "notification", "plugin", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-alert/issues", "source": "https://github.com/kartik-v/yii2-widget-alert/tree/v1.1.5"}, "time": "2021-10-16T10:23:22+00:00"}, {"name": "kartik-v/yii2-widget-colorinput", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-colorinput.git", "reference": "e35e6c7615a735b65557d6c38d112b77e2628c69"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-colorinput/zipball/e35e6c7615a735b65557d6c38d112b77e2628c69", "reference": "e35e6c7615a735b65557d6c38d112b77e2628c69", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\color\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced Yii 2 widget encapsulating the HTML 5 color input (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-colorinput", "keywords": ["HTML5", "color", "extension", "form", "input", "j<PERSON>y", "plugin", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-colorinput/issues", "source": "https://github.com/kartik-v/yii2-widget-colorinput/tree/v1.0.6"}, "time": "2020-10-23T17:50:44+00:00"}, {"name": "kartik-v/yii2-widget-datepicker", "version": "v1.4.8", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-datepicker.git", "reference": "f5f8b396cf03d4a383aad5e7b338f8cb065abf66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-datepicker/zipball/f5f8b396cf03d4a383aad5e7b338f8cb065abf66", "reference": "f5f8b396cf03d4a383aad5e7b338f8cb065abf66", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"kartik\\date\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the bootstrap datepicker plugin (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-datepicker", "keywords": ["date", "extension", "form", "j<PERSON>y", "picker", "plugin", "select2", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-datepicker/issues", "source": "https://github.com/kartik-v/yii2-widget-datepicker/tree/v1.4.8"}, "time": "2021-10-28T03:58:09+00:00"}, {"name": "kartik-v/yii2-widget-datetimepicker", "version": "v1.5.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-datetimepicker.git", "reference": "85b22d38553ca207f86be198f37e6531347e9a23"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-datetimepicker/zipball/85b22d38553ca207f86be198f37e6531347e9a23", "reference": "85b22d38553ca207f86be198f37e6531347e9a23", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=3.0.4"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"kartik\\datetime\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the bootstrap datetimepicker plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-datetimepicker", "keywords": ["datetime", "extension", "form", "j<PERSON>y", "picker", "plugin", "select2", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-datetimepicker/issues", "source": "https://github.com/kartik-v/yii2-widget-datetimepicker/tree/v1.5.1"}, "time": "2022-03-18T17:42:22+00:00"}, {"name": "kartik-v/yii2-widget-depdrop", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-depdrop.git", "reference": "ea347e3793fbd8273cc9bd1eb94c4b32bb55d318"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-depdrop/zipball/ea347e3793fbd8273cc9bd1eb94c4b32bb55d318", "reference": "ea347e3793fbd8273cc9bd1eb94c4b32bb55d318", "shasum": ""}, "require": {"kartik-v/dependent-dropdown": "~1.4", "kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\depdrop\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Widget that enables setting up dependent dropdowns with nested dependencies (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-depdrop", "keywords": ["dependent", "dropdown", "extension", "form", "j<PERSON>y", "plugin", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-depdrop/issues", "source": "https://github.com/kartik-v/yii2-widget-depdrop/tree/v1.0.6"}, "time": "2019-04-19T07:02:48+00:00"}, {"name": "kartik-v/yii2-widget-fileinput", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-fileinput.git", "reference": "b5500b6855526837154694c2afab8dbc3afc8abd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-fileinput/zipball/b5500b6855526837154694c2afab8dbc3afc8abd", "reference": "b5500b6855526837154694c2afab8dbc3afc8abd", "shasum": ""}, "require": {"kartik-v/bootstrap-fileinput": ">=5.5.0", "kartik-v/yii2-krajee-base": ">=3.0.5"}, "default-branch": true, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"kartik\\file\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced FileInput widget for Bootstrap 3.x, 4.x & 5.x with file preview, multiple selection, and more features (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-fileinput", "keywords": ["extension", "file", "form", "input", "j<PERSON>y", "plugin", "upload", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-fileinput/issues", "source": "https://github.com/kartik-v/yii2-widget-fileinput/tree/v1.1.1"}, "funding": [{"url": "https://opencollective.com/yii2-widget-fileinput", "type": "open_collective"}], "time": "2022-06-28T04:31:04+00:00"}, {"name": "kartik-v/yii2-widget-growl", "version": "v1.1.2", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-growl.git", "reference": "37e8f9f10d3bc9d71f3ef64c4aaa0e2fc83dd5dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-growl/zipball/37e8f9f10d3bc9d71f3ef64c4aaa0e2fc83dd5dc", "reference": "37e8f9f10d3bc9d71f3ef64c4aaa0e2fc83dd5dc", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"kartik\\growl\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A widget to generate growl based notifications using bootstrap-growl plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-growl", "keywords": ["alert", "bootstrap", "extension", "growl", "j<PERSON>y", "notification", "plugin", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-growl/issues", "source": "https://github.com/kartik-v/yii2-widget-growl/tree/v1.1.2"}, "time": "2021-05-19T12:44:49+00:00"}, {"name": "kartik-v/yii2-widget-rangeinput", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-rangeinput.git", "reference": "dd9019bab7e5bf570a02870d9e74387891bbdb32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-rangeinput/zipball/dd9019bab7e5bf570a02870d9e74387891bbdb32", "reference": "dd9019bab7e5bf570a02870d9e74387891bbdb32", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\range\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced Yii 2 widget encapsulating the HTML 5 range input (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-rangeinput", "keywords": ["HTML5", "extension", "form", "input", "j<PERSON>y", "plugin", "range", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-rangeinput/issues", "source": "https://github.com/kartik-v/yii2-widget-rangeinput/tree/master"}, "time": "2018-09-07T10:05:08+00:00"}, {"name": "kartik-v/yii2-widget-rating", "version": "v1.0.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-rating.git", "reference": "d3d7249490044f80e65f8f3938191f39a76586b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-rating/zipball/d3d7249490044f80e65f8f3938191f39a76586b2", "reference": "d3d7249490044f80e65f8f3938191f39a76586b2", "shasum": ""}, "require": {"kartik-v/bootstrap-star-rating": "~4.0", "kartik-v/yii2-krajee-base": ">=1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\rating\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A Yii2 widget for the simple yet powerful bootstrap-star-rating plugin with fractional rating support (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-rating", "keywords": ["Rating", "bootstrap", "extension", "form", "input", "j<PERSON>y", "plugin", "star", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-rating/issues", "source": "https://github.com/kartik-v/yii2-widget-rating/tree/v1.0.5"}, "time": "2021-11-20T05:26:05+00:00"}, {"name": "kartik-v/yii2-widget-select2", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-select2.git", "reference": "958ba374af39fbc88f1fd11209b22efe1055d567"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-select2/zipball/958ba374af39fbc88f1fd11209b22efe1055d567", "reference": "958ba374af39fbc88f1fd11209b22efe1055d567", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=3.0.4", "select2/select2": ">=4.0.0"}, "default-branch": true, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"kartik\\select2\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the Select2 jQuery plugin (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-select2", "keywords": ["dropdown", "extension", "form", "j<PERSON>y", "plugin", "select2", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-select2/issues", "source": "https://github.com/kartik-v/yii2-widget-select2/tree/master"}, "funding": [{"url": "https://opencollective.com/yii2-widget-select2", "type": "open_collective"}], "time": "2024-07-24T05:21:32+00:00"}, {"name": "kartik-v/yii2-widget-sidenav", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-sidenav.git", "reference": "87e9c815624aa966d70bb4507b3d53c158db0d43"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-sidenav/zipball/87e9c815624aa966d70bb4507b3d53c158db0d43", "reference": "87e9c815624aa966d70bb4507b3d53c158db0d43", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\sidenav\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced side navigation menu styled for bootstrap (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-sidenav", "keywords": ["bootstrap", "extension", "j<PERSON>y", "menu", "navigation", "plugin", "sidenav", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-sidenav/issues", "source": "https://github.com/kartik-v/yii2-widget-sidenav/tree/v1.0.1"}, "funding": [{"url": "https://opencollective.com/yii2-widget-sidenav", "type": "open_collective"}], "time": "2021-04-08T17:49:26+00:00"}, {"name": "kartik-v/yii2-widget-spinner", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-spinner.git", "reference": "eb10dad17a107bf14f173c99994770ca23c548a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-spinner/zipball/eb10dad17a107bf14f173c99994770ca23c548a6", "reference": "eb10dad17a107bf14f173c99994770ca23c548a6", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\spinner\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A widget to render animated CSS3 loading spinners with VML fallback for IE (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-spinner", "keywords": ["CSS3", "extension", "j<PERSON>y", "loading", "plugin", "spinner", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-spinner/issues", "source": "https://github.com/kartik-v/yii2-widget-spinner/tree/master"}, "time": "2018-10-09T11:54:03+00:00"}, {"name": "kartik-v/yii2-widget-switchinput", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-switchinput.git", "reference": "7d8ee999d79bcdc1601da5cd59439ac7eb1f5ea6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-switchinput/zipball/7d8ee999d79bcdc1601da5cd59439ac7eb1f5ea6", "reference": "7d8ee999d79bcdc1601da5cd59439ac7eb1f5ea6", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\switchinput\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A Yii2 wrapper widget for the Bootstrap Switch plugin to use checkboxes & radios as toggle switchinputes (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-switchinput", "keywords": ["bootstrap", "extension", "form", "input", "j<PERSON>y", "plugin", "switchinput", "toggle", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-switchinput/issues", "source": "https://github.com/kartik-v/yii2-widget-switchinput/tree/master"}, "time": "2016-01-10T16:47:35+00:00"}, {"name": "kartik-v/yii2-widget-timepicker", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-timepicker.git", "reference": "680aec2d79846e926c072da455cf6f33e1c3bb12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-timepicker/zipball/680aec2d79846e926c072da455cf6f33e1c3bb12", "reference": "680aec2d79846e926c072da455cf6f33e1c3bb12", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "default-branch": true, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\time\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the bootstrap timepicker plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-timepicker", "keywords": ["bootstrap", "extension", "form", "j<PERSON>y", "picker", "plugin", "time", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-timepicker/issues", "source": "https://github.com/kartik-v/yii2-widget-timepicker/tree/v1.0.5"}, "time": "2021-10-28T03:49:56+00:00"}, {"name": "kartik-v/yii2-widget-touchspin", "version": "v1.2.4", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-touchspin.git", "reference": "1eec4c3f3a8bf9a170e1e0682c2c89f2929d65e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-touchspin/zipball/1eec4c3f3a8bf9a170e1e0682c2c89f2929d65e9", "reference": "1eec4c3f3a8bf9a170e1e0682c2c89f2929d65e9", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=3.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"kartik\\touchspin\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A Yii2 wrapper widget for the Bootstrap Switch plugin to use checkboxes & radios as toggle touchspines (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-touchspin", "keywords": ["bootstrap", "extension", "form", "input", "j<PERSON>y", "plugin", "spinner", "touch", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-touchspin/issues", "source": "https://github.com/kartik-v/yii2-widget-touchspin/tree/v1.2.4"}, "time": "2021-09-02T12:50:50+00:00"}, {"name": "kartik-v/yii2-widget-typeahead", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-typeahead.git", "reference": "7b7041a3cbbeb2db0a608e9f6c9b3f4f63b0069d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-typeahead/zipball/7b7041a3cbbeb2db0a608e9f6c9b3f4f63b0069d", "reference": "7b7041a3cbbeb2db0a608e9f6c9b3f4f63b0069d", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\typeahead\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the Twitter Typeahead plugin (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-typeahead", "keywords": ["dropdown", "extension", "form", "j<PERSON>y", "plugin", "typeahead", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-typeahead/issues", "source": "https://github.com/kartik-v/yii2-widget-typeahead/tree/master"}, "time": "2019-05-29T12:06:56+00:00"}, {"name": "kartik-v/yii2-widgets", "version": "v3.4.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widgets.git", "reference": "e5a030d700243a90eccf96a070380bd3b76e17a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widgets/zipball/e5a030d700243a90eccf96a070380bd3b76e17a3", "reference": "e5a030d700243a90eccf96a070380bd3b76e17a3", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*", "kartik-v/yii2-widget-activeform": "*", "kartik-v/yii2-widget-affix": "*", "kartik-v/yii2-widget-alert": "*", "kartik-v/yii2-widget-colorinput": "*", "kartik-v/yii2-widget-datepicker": "*", "kartik-v/yii2-widget-datetimepicker": "*", "kartik-v/yii2-widget-depdrop": "*", "kartik-v/yii2-widget-fileinput": "*", "kartik-v/yii2-widget-growl": "*", "kartik-v/yii2-widget-rangeinput": "*", "kartik-v/yii2-widget-rating": "*", "kartik-v/yii2-widget-select2": "*", "kartik-v/yii2-widget-sidenav": "*", "kartik-v/yii2-widget-spinner": "*", "kartik-v/yii2-widget-switchinput": "*", "kartik-v/yii2-widget-timepicker": "*", "kartik-v/yii2-widget-touchspin": "*", "kartik-v/yii2-widget-typeahead": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\widgets\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Collection of useful widgets for Yii Framework 2.0 extending functionalities for Bootstrap", "homepage": "https://github.com/kartik-v/yii2-widgets", "keywords": ["extension", "form", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widgets/issues", "source": "https://github.com/kartik-v/yii2-widgets/tree/master"}, "time": "2018-10-09T17:40:19+00:00"}, {"name": "maennchen/zipstream-php", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "9712d8fa4cdf9240380b01eb4be55ad8dcf71416"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/9712d8fa4cdf9240380b01eb4be55ad8dcf71416", "reference": "9712d8fa4cdf9240380b01eb4be55ad8dcf71416", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-zlib": "*", "php-64bit": "^8.3"}, "require-dev": {"brianium/paratest": "^7.7", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.16", "guzzlehttp/guzzle": "^7.5", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.5", "phpunit/phpunit": "^12.0", "vimeo/psalm": "^6.0"}, "suggest": {"guzzlehttp/psr7": "^2.4", "psr/http-message": "^2.0"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/3.2.0"}, "funding": [{"url": "https://github.com/maennchen", "type": "github"}], "time": "2025-07-17T11:15:13+00:00"}, {"name": "markbaker/complex", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/3.0.2"}, "time": "2022-12-06T16:21:08+00:00"}, {"name": "markbaker/matrix", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "728434227fe21be27ff6d86621a1b13107a2562c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/728434227fe21be27ff6d86621a1b13107a2562c", "reference": "728434227fe21be27ff6d86621a1b13107a2562c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/3.0.1"}, "time": "2022-12-02T22:17:43+00:00"}, {"name": "michelf/php-markdown", "version": "1.9.1", "source": {"type": "git", "url": "https://github.com/michelf/php-markdown.git", "reference": "5024d623c1a057dcd2d076d25b7d270a1d0d55f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/michelf/php-markdown/zipball/5024d623c1a057dcd2d076d25b7d270a1d0d55f3", "reference": "5024d623c1a057dcd2d076d25b7d270a1d0d55f3", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": ">=4.3 <5.8"}, "type": "library", "autoload": {"psr-4": {"Michelf\\": "<PERSON><PERSON>/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://michelf.ca/", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://daringfireball.net/"}], "description": "PHP <PERSON>", "homepage": "https://michelf.ca/projects/php-markdown/", "keywords": ["markdown"], "support": {"issues": "https://github.com/michelf/php-markdown/issues", "source": "https://github.com/michelf/php-markdown/tree/1.9.1"}, "time": "2021-11-24T02:52:38+00:00"}, {"name": "michelf/php-smartypants", "version": "1.8.1", "source": {"type": "git", "url": "https://github.com/michelf/php-smartypants.git", "reference": "47d17c90a4dfd0ccf1f87e25c65e6c8012415aad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/michelf/php-smartypants/zipball/47d17c90a4dfd0ccf1f87e25c65e6c8012415aad", "reference": "47d17c90a4dfd0ccf1f87e25c65e6c8012415aad", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"psr-0": {"Michelf": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://michelf.ca/", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://daringfireball.net/"}], "description": "PHP SmartyPants", "homepage": "https://michelf.ca/projects/php-smartypants/", "keywords": ["dashes", "quotes", "spaces", "typographer", "typography"], "support": {"issues": "https://github.com/michelf/php-smartypants/issues", "source": "https://github.com/michelf/php-smartypants/tree/1.8.1"}, "time": "2016-12-13T01:01:17+00:00"}, {"name": "mpdf/mpdf", "version": "v8.2.5", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "e175b05e3e00977b85feb96a8cccb174ac63621f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/e175b05e3e00977b85feb96a8cccb174ac63621f", "reference": "e175b05e3e00977b85feb96a8cccb174ac63621f", "shasum": ""}, "require": {"ext-gd": "*", "ext-mbstring": "*", "mpdf/psr-http-message-shim": "^1.0 || ^2.0", "mpdf/psr-log-aware-trait": "^2.0 || ^3.0", "myclabs/deep-copy": "^1.7", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": "^5.6 || ^7.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/http-message": "^1.0 || ^2.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "setasign/fpdi": "^2.1"}, "require-dev": {"mockery/mockery": "^1.3.0", "mpdf/qrcode": "^1.1.0", "squizlabs/php_codesniffer": "^3.5.0", "tracy/tracy": "~2.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-bcmath": "Needed for generation of some types of barcodes", "ext-xml": "Needed mainly for SVG manipulation", "ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Mpdf\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-only"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>, maintainer"}, {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON> (retired)"}], "description": "PHP library generating PDF files from UTF-8 encoded HTML", "homepage": "https://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"], "support": {"docs": "https://mpdf.github.io", "issues": "https://github.com/mpdf/mpdf/issues", "source": "https://github.com/mpdf/mpdf"}, "funding": [{"url": "https://www.paypal.me/mpdf", "type": "custom"}], "time": "2024-11-18T15:30:42+00:00"}, {"name": "mpdf/psr-http-message-shim", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/mpdf/psr-http-message-shim.git", "reference": "f25a0153d645e234f9db42e5433b16d9b113920f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/psr-http-message-shim/zipball/f25a0153d645e234f9db42e5433b16d9b113920f", "reference": "f25a0153d645e234f9db42e5433b16d9b113920f", "shasum": ""}, "require": {"psr/http-message": "^2.0"}, "type": "library", "autoload": {"psr-4": {"Mpdf\\PsrHttpMessageShim\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Shim to allow support of different psr/message versions.", "support": {"issues": "https://github.com/mpdf/psr-http-message-shim/issues", "source": "https://github.com/mpdf/psr-http-message-shim/tree/v2.0.1"}, "time": "2023-10-02T14:34:03+00:00"}, {"name": "mpdf/psr-log-aware-trait", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/mpdf/psr-log-aware-trait.git", "reference": "a633da6065e946cc491e1c962850344bb0bf3e78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/psr-log-aware-trait/zipball/a633da6065e946cc491e1c962850344bb0bf3e78", "reference": "a633da6065e946cc491e1c962850344bb0bf3e78", "shasum": ""}, "require": {"psr/log": "^3.0"}, "type": "library", "autoload": {"psr-4": {"Mpdf\\PsrLogAwareTrait\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Trait to allow support of different psr/log versions.", "support": {"issues": "https://github.com/mpdf/psr-log-aware-trait/issues", "source": "https://github.com/mpdf/psr-log-aware-trait/tree/v3.0.0"}, "time": "2023-05-03T06:19:36+00:00"}, {"name": "myclabs/deep-copy", "version": "1.13.3", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "faed855a7b5f4d4637717c2b3863e277116beb36"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/faed855a7b5f4d4637717c2b3863e277116beb36", "reference": "faed855a7b5f4d4637717c2b3863e277116beb36", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.13.3"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2025-07-05T12:25:42+00:00"}, {"name": "npm-asset/bootstrap", "version": "4.6.2", "dist": {"type": "tar", "url": "https://registry.npmjs.org/bootstrap/-/bootstrap-4.6.2.tgz"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/fullcalendar", "version": "3.8.0", "dist": {"type": "tar", "url": "https://registry.npmjs.org/fullcalendar/-/fullcalendar-3.8.0.tgz"}, "require": {"npm-asset/jquery": ">=2,<4.0", "npm-asset/moment": ">=2.9.0,<3.0.0"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/jquery", "version": "3.7.1", "dist": {"type": "tar", "url": "https://registry.npmjs.org/jquery/-/jquery-3.7.1.tgz"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/moment", "version": "2.30.1", "dist": {"type": "tar", "url": "https://registry.npmjs.org/moment/-/moment-2.30.1.tgz"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "4.5.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "2ea9786632e6fac1aee601b6e426bcc723d8ce13"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/2ea9786632e6fac1aee601b6e426bcc723d8ce13", "reference": "2ea9786632e6fac1aee601b6e426bcc723d8ce13", "shasum": ""}, "require": {"composer/pcre": "^1||^2||^3", "ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "maennchen/zipstream-php": "^2.1 || ^3.0", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^8.1", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-main", "dompdf/dompdf": "^2.0 || ^3.0", "friendsofphp/php-cs-fixer": "^3.2", "mitoteam/jpgraph": "^10.3", "mpdf/mpdf": "^8.1.1", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^1.1 || ^2.0", "phpstan/phpstan-deprecation-rules": "^1.0 || ^2.0", "phpstan/phpstan-phpunit": "^1.0 || ^2.0", "phpunit/phpunit": "^10.5", "squizlabs/php_codesniffer": "^3.7", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "ext-intl": "PHP Internationalization Functions", "mitoteam/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/4.5.0"}, "time": "2025-07-24T05:15:59+00:00"}, {"name": "phpspec/php-diff", "version": "v1.1.3", "source": {"type": "git", "url": "https://github.com/phpspec/php-diff.git", "reference": "fc1156187f9f6c8395886fe85ed88a0a245d72e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/php-diff/zipball/fc1156187f9f6c8395886fe85ed88a0a245d72e9", "reference": "fc1156187f9f6c8395886fe85ed88a0a245d72e9", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"Diff": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://github.com/chrisboulton"}], "description": "A comprehensive library for generating differences between two hashable objects (strings or arrays).", "support": {"source": "https://github.com/phpspec/php-diff/tree/v1.1.3"}, "time": "2020-09-18T13:47:07+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "psr/simple-cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "time": "2021-10-29T13:26:27+00:00"}, {"name": "raoul2000/yii2-workflow", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/raoul2000/yii2-workflow.git", "reference": "1e21516d0f2e102664cf194c33f2d74937eb7fd4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/raoul2000/yii2-workflow/zipball/1e21516d0f2e102664cf194c33f2d74937eb7fd4", "reference": "1e21516d0f2e102664cf194c33f2d74937eb7fd4", "shasum": ""}, "require": {"php": ">=5.4.0", "yiisoft/yii2": "~2.0.13"}, "require-dev": {"codeception/assert-throws": "^1.0", "codeception/codeception": ">= 2.0.9", "codeception/specify": "*", "codeception/verify": "*", "myclabs/deep-copy": ">= 1.3.1", "yiisoft/yii2-codeception": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"raoul2000\\workflow\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/raoul2000/yii2-workflow"}], "description": "A simple workflow engine for Yii2", "homepage": "http://raoul2000.github.io/yii2-workflow/guide-README.html", "keywords": ["workflow", "yii2"], "support": {"email": "<EMAIL>", "issues": "https://github.com/raoul2000/yii2-workflow/issues", "source": "https://github.com/raoul2000/yii2-workflow"}, "time": "2018-06-08T11:49:04+00:00"}, {"name": "raoul2000/yii2-workflow-view", "version": "0.0.2", "source": {"type": "git", "url": "https://github.com/raoul2000/yii2-workflow-view.git", "reference": "0ea0b90a7595ba425baae50405c4e89a1ded1b96"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/raoul2000/yii2-workflow-view/zipball/0ea0b90a7595ba425baae50405c4e89a1ded1b96", "reference": "0ea0b90a7595ba425baae50405c4e89a1ded1b96", "shasum": ""}, "require": {"bower-asset/vis": "*", "php": ">=5.4.0", "raoul2000/yii2-workflow": ">=0.0.18", "yiisoft/yii2": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"raoul2000\\workflow\\view\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/raoul2000/yii2-workflow-view"}], "description": "A simple widget to display your workflows", "homepage": "http://www.yiiframework.com/", "keywords": ["view", "widget", "workflow", "yii2"], "support": {"email": "<EMAIL>", "issues": "https://github.com/raoul2000/yii2-workflow-view/issues", "source": "https://github.com/raoul2000/yii2-workflow-view"}, "time": "2015-08-23T20:00:46+00:00"}, {"name": "rmrevin/yii2-fontawesome", "version": "2.10.3", "source": {"type": "git", "url": "https://github.com/rmrevin/yii2-fontawesome.git", "reference": "ce4dd62a048090f3d98822d4bc9401bfcdde1f98"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rmrevin/yii2-fontawesome/zipball/ce4dd62a048090f3d98822d4bc9401bfcdde1f98", "reference": "ce4dd62a048090f3d98822d4bc9401bfcdde1f98", "shasum": ""}, "require": {"bower-asset/fontawesome": "4.3.*", "php": ">=5.4.0", "yiisoft/yii2": "2.0.*"}, "type": "yii2-extension", "extra": {"asset-installer-paths": {"npm-asset-library": "vendor/npm", "bower-asset-library": "vendor/bower"}}, "autoload": {"psr-4": {"rmrevin\\yii\\fontawesome\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://rmrevin.ru/"}], "description": "Asset Bundle for Yii2 with Font Awesome", "keywords": ["asset", "awesome", "bundle", "font", "yii"], "support": {"issues": "https://github.com/rmrevin/yii2-fontawesome/issues", "source": "https://github.com/rmrevin/yii2-fontawesome"}, "time": "2015-06-23T13:06:14+00:00"}, {"name": "select2/select2", "version": "4.0.13", "source": {"type": "git", "url": "https://github.com/select2/select2.git", "reference": "45f2b83ceed5231afa7b3d5b12b58ad335edd82e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/select2/select2/zipball/45f2b83ceed5231afa7b3d5b12b58ad335edd82e", "reference": "45f2b83ceed5231afa7b3d5b12b58ad335edd82e", "shasum": ""}, "type": "component", "extra": {"component": {"files": ["dist/js/select2.js", "dist/js/i18n/*.js", "dist/css/select2.css"], "styles": ["dist/css/select2.css"], "scripts": ["dist/js/select2.js"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Select2 is a jQuery based replacement for select boxes.", "homepage": "https://select2.org/", "support": {"issues": "https://github.com/select2/select2/issues", "source": "https://github.com/select2/select2/tree/4.0.13"}, "time": "2020-01-28T05:01:22+00:00"}, {"name": "setasign/fpdi", "version": "v2.6.3", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "67c31f5e50c93c20579ca9e23035d8c540b51941"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/67c31f5e50c93c20579ca9e23035d8c540b51941", "reference": "67c31f5e50c93c20579ca9e23035d8c540b51941", "shasum": ""}, "require": {"ext-zlib": "*", "php": "^7.1 || ^8.0"}, "conflict": {"setasign/tfpdf": "<1.31"}, "require-dev": {"phpunit/phpunit": "^7", "setasign/fpdf": "~1.8.6", "setasign/tfpdf": "~1.33", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.2"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured."}, "type": "library", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"issues": "https://github.com/Setasign/FPDI/issues", "source": "https://github.com/Setasign/FPDI/tree/v2.6.3"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/setasign/fpdi", "type": "tidelift"}], "time": "2025-02-05T13:22:35+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/63afe740e99a13ba87ec199bb07bbdee937a5b62", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/event-dispatcher", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "497f73ac996a598c92409b44ac43b6690c4f666d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/497f73ac996a598c92409b44ac43b6690c4f666d", "reference": "497f73ac996a598c92409b44ac43b6690c4f666d", "shasum": ""}, "require": {"php": ">=8.2", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/error-handler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-22T09:11:45+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "59eb412e93815df44f05f342958efa9f46b1e586"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/59eb412e93815df44f05f342958efa9f46b1e586", "reference": "59eb412e93815df44f05f342958efa9f46b1e586", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/mailer", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "b5db5105b290bdbea5ab27b89c69effcf1cb3368"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/b5db5105b290bdbea5ab27b89c69effcf1cb3368", "reference": "b5db5105b290bdbea5ab27b89c69effcf1cb3368", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.2", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/mime": "^7.2", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/messenger": "<6.4", "symfony/mime": "<6.4", "symfony/twig-bridge": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/twig-bridge": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T19:55:54+00:00"}, {"name": "symfony/mime", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "0e7b19b2f399c31df0cdbe5d8cbf53f02f6cfcd9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/0e7b19b2f399c31df0cdbe5d8cbf53f02f6cfcd9", "reference": "0e7b19b2f399c31df0cdbe5d8cbf53f02f6cfcd9", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<6.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-19T08:51:26+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/9614ac4d8061dc257ecc64cba1b140873dce8ad3", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-10T14:38:51+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-23T08:48:59+00:00"}, {"name": "symfony/service-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-25T09:37:31+00:00"}, {"name": "twig/twig", "version": "v3.21.1", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "285123877d4dd97dd7c11842ac5fb7e86e60d81d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/285123877d4dd97dd7c11842ac5fb7e86e60d81d", "reference": "285123877d4dd97dd7c11842ac5fb7e86e60d81d", "shasum": ""}, "require": {"php": ">=8.1.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"phpstan/phpstan": "^2.0", "psr/container": "^1.0|^2.0", "symfony/phpunit-bridge": "^5.4.9|^6.4|^7.0"}, "type": "library", "autoload": {"files": ["src/Resources/core.php", "src/Resources/debug.php", "src/Resources/escaper.php", "src/Resources/string_loader.php"], "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.21.1"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2025-05-03T07:21:55+00:00"}, {"name": "yiisoft/yii2", "version": "2.0.53", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-framework.git", "reference": "6c622fb8243181d7912b62ad80821cc0e1c745db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-framework/zipball/6c622fb8243181d7912b62ad80821cc0e1c745db", "reference": "6c622fb8243181d7912b62ad80821cc0e1c745db", "shasum": ""}, "require": {"bower-asset/inputmask": "^5.0.8 ", "bower-asset/jquery": "3.7.*@stable | 3.6.*@stable | 3.5.*@stable | 3.4.*@stable | 3.3.*@stable | 3.2.*@stable | 3.1.*@stable | 2.2.*@stable | 2.1.*@stable | 1.11.*@stable | 1.12.*@stable", "bower-asset/punycode": "^1.4", "bower-asset/yii2-pjax": "~2.0.1", "cebe/markdown": "~1.0.0 | ~1.1.0 | ~1.2.0", "ext-ctype": "*", "ext-mbstring": "*", "ezyang/htmlpurifier": "^4.17", "lib-pcre": "*", "php": ">=7.3.0", "yiisoft/yii2-composer": "~2.0.4"}, "bin": ["yii"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.yiiframework.com/", "role": "Founder and project lead"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://rmcreative.ru/", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://mdomba.info/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.cebe.cc/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://resurtm.com/", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://dynasource.eu", "role": "Core framework development"}], "description": "Yii PHP Framework Version 2", "homepage": "https://www.yiiframework.com/", "keywords": ["framework", "yii2"], "support": {"forum": "https://forum.yiiframework.com/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2/issues?state=open", "source": "https://github.com/yiisoft/yii2", "wiki": "https://www.yiiframework.com/wiki"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2", "type": "tidelift"}], "time": "2025-06-27T07:42:53+00:00"}, {"name": "yiisoft/yii2-bootstrap4", "version": "2.0.12", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-bootstrap4.git", "reference": "49f9b70de3f5ab55ea1dc1ea57021a6bf91b3102"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-bootstrap4/zipball/49f9b70de3f5ab55ea1dc1ea57021a6bf91b3102", "reference": "49f9b70de3f5ab55ea1dc1ea57021a6bf91b3102", "shasum": ""}, "require": {"npm-asset/bootstrap": "^4.3", "yiisoft/yii2": "^2.0.43"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34", "yiisoft/yii2-coding-standards": "~2.0"}, "type": "yii2-extension", "extra": {"patches": {"phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}, "phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}}, "branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"yii\\bootstrap4\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.yiiframework.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://rmcreative.ru/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Twitter Bootstrap extension for the Yii framework", "keywords": ["bootstrap", "bootstrap4", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-bootstrap4/issues", "source": "https://github.com/yiisoft/yii2-bootstrap4", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-bootstrap4", "type": "tidelift"}], "time": "2025-02-13T21:11:27+00:00"}, {"name": "yiisoft/yii2-bootstrap5", "version": "2.0.50", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-bootstrap5.git", "reference": "ad080a7ea063074888c2d801f6b05162064f8ae0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-bootstrap5/zipball/ad080a7ea063074888c2d801f6b05162064f8ae0", "reference": "ad080a7ea063074888c2d801f6b05162064f8ae0", "shasum": ""}, "require": {"bower-asset/bootstrap": "^5.2.3", "ext-json": "*", "php": ">=7.3", "yiisoft/yii2": "^2.0.42"}, "require-dev": {"phpunit/phpunit": "^9.6", "twbs/bootstrap-icons": "^1.7.2", "yiisoft/yii2-coding-standards": "~2.0"}, "suggest": {"twbs/bootstrap-icons": "Add this package to the `require` section of your `composer.json` if you'd like to use the bootstrap icon asset."}, "type": "yii2-extension", "extra": {"bootstrap": "yii\\bootstrap5\\i18n\\TranslationBootstrap", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\bootstrap5\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://net23.ru/"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Twitter Bootstrap v5 extension for the Yii framework", "keywords": ["bootstrap", "bootstrap5", "yii2"], "support": {"issues": "https://github.com/yiisoft/yii2-bootstrap5/issues", "source": "https://github.com/yiisoft/yii2-bootstrap5"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-bootstrap5", "type": "tidelift"}], "time": "2025-04-10T08:03:20+00:00"}, {"name": "yiisoft/yii2-composer", "version": "2.0.11", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-composer.git", "reference": "b684b01ecb119c8287721def726a0e24fec2fef2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-composer/zipball/b684b01ecb119c8287721def726a0e24fec2fef2", "reference": "b684b01ecb119c8287721def726a0e24fec2fef2", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 | ^2.0"}, "require-dev": {"composer/composer": "^1.0 | ^2.0@dev", "phpunit/phpunit": "<7"}, "type": "composer-plugin", "extra": {"class": "yii\\composer\\Plugin", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\composer\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The composer plugin for Yii extension installer", "keywords": ["composer", "extension installer", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-composer/issues", "source": "https://github.com/yiisoft/yii2-composer", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-composer", "type": "tidelift"}], "time": "2025-02-13T20:59:36+00:00"}, {"name": "yiisoft/yii2-debug", "version": "2.1.27", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-debug.git", "reference": "44e158914911ef81cd7111fd6d46b918f65fae7c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-debug/zipball/44e158914911ef81cd7111fd6d46b918f65fae7c", "reference": "44e158914911ef81cd7111fd6d46b918f65fae7c", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4", "yiisoft/yii2": "~2.0.13"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34", "yiisoft/yii2-coding-standards": "~2.0", "yiisoft/yii2-swiftmailer": "*"}, "type": "yii2-extension", "extra": {"patches": {"phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch", "Fix PHP 8.1 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php81.patch"}, "phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}}, "branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true}, "autoload": {"psr-4": {"yii\\debug\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The debugger extension for the Yii framework", "keywords": ["debug", "debugger", "dev", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-debug/issues", "source": "https://github.com/yiisoft/yii2-debug", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-debug", "type": "tidelift"}], "time": "2025-06-08T13:32:11+00:00"}, {"name": "yiisoft/yii2-gii", "version": "2.2.7", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-gii.git", "reference": "f17c7ef7ef3081213f612b37ad0ceaa8e8cd3d3b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-gii/zipball/f17c7ef7ef3081213f612b37ad0ceaa8e8cd3d3b", "reference": "f17c7ef7ef3081213f612b37ad0ceaa8e8cd3d3b", "shasum": ""}, "require": {"phpspec/php-diff": "^1.1.0", "yiisoft/yii2": "~2.0.46"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34", "yiisoft/yii2-coding-standards": "~2.0"}, "type": "yii2-extension", "extra": {"patches": {"phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch", "Fix PHP 8.1 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php81.patch"}, "phpunit/php-file-iterator": {"Fix PHP 8.1 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_path_file_iterator.patch"}, "phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}}, "branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true}, "autoload": {"psr-4": {"yii\\gii\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The Gii extension for the Yii framework", "keywords": ["code generator", "dev", "gii", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-gii/issues", "source": "https://github.com/yiisoft/yii2-gii", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-gii", "type": "tidelift"}], "time": "2025-02-13T21:21:17+00:00"}, {"name": "yiisoft/yii2-httpclient", "version": "2.0.16", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-httpclient.git", "reference": "46c29475e19528f0c95f1c6ba0a89f172ebde8c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-httpclient/zipball/46c29475e19528f0c95f1c6ba0a89f172ebde8c1", "reference": "46c29475e19528f0c95f1c6ba0a89f172ebde8c1", "shasum": ""}, "require": {"paragonie/random_compat": ">=1", "yiisoft/yii2": "~2.0.13"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34"}, "type": "yii2-extension", "extra": {"patches": {"phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}, "phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}}, "branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true}, "autoload": {"psr-4": {"yii\\httpclient\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "HTTP client extension for the Yii framework", "keywords": ["curl", "http", "httpclient", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-httpclient/issues", "source": "https://github.com/yiisoft/yii2-httpclient", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-httpclient", "type": "tidelift"}], "time": "2025-02-13T21:09:57+00:00"}, {"name": "yiisoft/yii2-jui", "version": "2.0.7", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-jui.git", "reference": "ce45c16d4fbbe7d1c516d8d0e8311e07f6138eed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-jui/zipball/ce45c16d4fbbe7d1c516d8d0e8311e07f6138eed", "reference": "ce45c16d4fbbe7d1c516d8d0e8311e07f6138eed", "shasum": ""}, "require": {"bower-asset/jquery-ui": "~1.12.1", "yiisoft/yii2": "~2.0.4"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\jui\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The Jquery UI extension for the Yii framework", "keywords": ["jQuery <PERSON>", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-jui/issues", "source": "https://github.com/yiisoft/yii2-jui", "wiki": "http://www.yiiframework.com/wiki/"}, "time": "2017-11-25T15:32:29+00:00"}, {"name": "yiisoft/yii2-symfonymailer", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-symfonymailer.git", "reference": "82f5902551a160633c4734b5096977ce76a809d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-symfonymailer/zipball/82f5902551a160633c4734b5096977ce76a809d9", "reference": "82f5902551a160633c4734b5096977ce76a809d9", "shasum": ""}, "require": {"php": ">=7.4.0", "symfony/mailer": ">=5.4.0", "yiisoft/yii2": ">=2.0.4"}, "require-dev": {"phpunit/phpunit": "9.5.10"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\symfonymailer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The SymfonyMailer integration for the Yii framework", "keywords": ["email", "mail", "mailer", "symfony", "symfonymailer", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-symfonymailer/issues", "source": "https://github.com/yiisoft/yii2-symfonymailer", "wiki": "http://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-symfonymailer", "type": "tidelift"}], "time": "2022-09-04T10:48:21+00:00"}, {"name": "yiisoft/yii2-twig", "version": "2.5.1", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-twig.git", "reference": "dbc0fb3f4fc9582e67b5982e4e480083eaeee592"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-twig/zipball/dbc0fb3f4fc9582e67b5982e4e480083eaeee592", "reference": "dbc0fb3f4fc9582e67b5982e4e480083eaeee592", "shasum": ""}, "require": {"php": "^7.2.5|^8.0|^8.1", "twig/twig": "~3.9", "yiisoft/yii2": "~2.0.4"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34"}, "type": "yii2-extension", "extra": {"patches": {"phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch", "Fix PHP 8.1 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php81.patch"}, "phpunit/php-file-iterator": {"Fix PHP 8.1 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_path_file_iterator.patch"}, "phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}}, "branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true}, "autoload": {"psr-4": {"yii\\twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Twig integration for the Yii framework", "keywords": ["renderer", "twig", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-twig/issues", "source": "https://github.com/yiisoft/yii2-twig", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-twig", "type": "tidelift"}], "time": "2025-02-13T21:31:39+00:00"}], "packages-dev": [{"name": "behat/gherkin", "version": "v4.14.0", "source": {"type": "git", "url": "https://github.com/Behat/Gherkin.git", "reference": "34c9b59c59355a7b4c53b9f041c8dbd1c8acc3b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Gherkin/zipball/34c9b59c59355a7b4c53b9f041c8dbd1c8acc3b4", "reference": "34c9b59c59355a7b4c53b9f041c8dbd1c8acc3b4", "shasum": ""}, "require": {"composer-runtime-api": "^2.2", "php": "8.1.* || 8.2.* || 8.3.* || 8.4.*"}, "require-dev": {"cucumber/gherkin-monorepo": "dev-gherkin-v32.1.1", "friendsofphp/php-cs-fixer": "^3.65", "mikey179/vfsstream": "^1.6", "phpstan/extension-installer": "^1", "phpstan/phpstan": "^2", "phpstan/phpstan-phpunit": "^2", "phpunit/phpunit": "^10.5", "symfony/yaml": "^5.4 || ^6.4 || ^7.0"}, "suggest": {"symfony/yaml": "If you want to parse features, represented in YAML files"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"Behat\\Gherkin\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://everzet.com"}], "description": "Gherkin DSL parser for PHP", "homepage": "https://behat.org/", "keywords": ["BDD", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>ber", "DSL", "g<PERSON>kin", "parser"], "support": {"issues": "https://github.com/Behat/Gher<PERSON>/issues", "source": "https://github.com/Behat/Gherkin/tree/v4.14.0"}, "time": "2025-05-23T15:06:40+00:00"}, {"name": "codeception/codeception", "version": "5.3.2", "source": {"type": "git", "url": "https://github.com/Codeception/Codeception.git", "reference": "582112d7a603d575e41638df1e96900b10ae91b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Codeception/zipball/582112d7a603d575e41638df1e96900b10ae91b8", "reference": "582112d7a603d575e41638df1e96900b10ae91b8", "shasum": ""}, "require": {"behat/gherkin": "^4.12", "codeception/lib-asserts": "^2.2", "codeception/stub": "^4.1", "ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "php": "^8.2", "phpunit/php-code-coverage": "^9.2 | ^10.0 | ^11.0 | ^12.0", "phpunit/php-text-template": "^2.0 | ^3.0 | ^4.0 | ^5.0", "phpunit/php-timer": "^5.0.3 | ^6.0 | ^7.0 | ^8.0", "phpunit/phpunit": "^9.5.20 | ^10.0 | ^11.0 | ^12.0", "psy/psysh": "^0.11.2 | ^0.12", "sebastian/comparator": "^4.0.5 | ^5.0 | ^6.0 | ^7.0", "sebastian/diff": "^4.0.3 | ^5.0 | ^6.0 | ^7.0", "symfony/console": ">=5.4.24 <8.0", "symfony/css-selector": ">=5.4.24 <8.0", "symfony/event-dispatcher": ">=5.4.24 <8.0", "symfony/finder": ">=5.4.24 <8.0", "symfony/var-dumper": ">=5.4.24 <8.0", "symfony/yaml": ">=5.4.24 <8.0"}, "conflict": {"codeception/lib-innerbrowser": "<3.1.3", "codeception/module-filesystem": "<3.0", "codeception/module-phpbrowser": "<2.5"}, "replace": {"codeception/phpunit-wrapper": "*"}, "require-dev": {"codeception/lib-innerbrowser": "*@dev", "codeception/lib-web": "*@dev", "codeception/module-asserts": "*@dev", "codeception/module-cli": "*@dev", "codeception/module-db": "*@dev", "codeception/module-filesystem": "*@dev", "codeception/module-phpbrowser": "*@dev", "codeception/module-webdriver": "*@dev", "codeception/util-universalframework": "*@dev", "doctrine/orm": "^3.3", "ext-simplexml": "*", "jetbrains/phpstorm-attributes": "^1.0", "laravel-zero/phar-updater": "^1.4", "php-webdriver/webdriver": "^1.15", "stecman/symfony-console-completion": "^0.14", "symfony/dotenv": ">=5.4.24 <8.0", "symfony/error-handler": ">=5.4.24 <8.0", "symfony/process": ">=5.4.24 <8.0", "vlucas/phpdotenv": "^5.1"}, "suggest": {"codeception/specify": "BDD-style code blocks", "codeception/verify": "BDD-style assertions", "ext-simplexml": "For loading params from XML files", "stecman/symfony-console-completion": "For BASH autocompletion", "symfony/dotenv": "For loading params from .env files", "symfony/phpunit-bridge": "For phpunit-bridge support", "vlucas/phpdotenv": "For loading params from .env files"}, "bin": ["codecept"], "type": "library", "extra": {"branch-alias": {"dev-main": "5.2.x-dev"}}, "autoload": {"files": ["functions.php"], "psr-4": {"Codeception\\": "src/Codeception", "Codeception\\Extension\\": "ext"}, "classmap": ["src/PHPUnit/TestCase.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://codeception.com"}], "description": "BDD-style testing framework", "homepage": "https://codeception.com/", "keywords": ["BDD", "TDD", "acceptance testing", "functional testing", "unit testing"], "support": {"issues": "https://github.com/Codeception/Codeception/issues", "source": "https://github.com/Codeception/Codeception/tree/5.3.2"}, "funding": [{"url": "https://opencollective.com/codeception", "type": "open_collective"}], "time": "2025-05-26T07:47:39+00:00"}, {"name": "codeception/lib-asserts", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/Codeception/lib-asserts.git", "reference": "06750a60af3ebc66faab4313981accec1be4eefc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/lib-asserts/zipball/06750a60af3ebc66faab4313981accec1be4eefc", "reference": "06750a60af3ebc66faab4313981accec1be4eefc", "shasum": ""}, "require": {"codeception/phpunit-wrapper": "^7.7.1 | ^8.0.3 | ^9.0", "ext-dom": "*", "php": "^7.4 | ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://codegyre.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "homepage": "https://medium.com/@ganieves"}], "description": "Assertion methods used by Codeception core and Asserts module", "homepage": "https://codeception.com/", "keywords": ["codeception"], "support": {"issues": "https://github.com/Codeception/lib-asserts/issues", "source": "https://github.com/Codeception/lib-asserts/tree/2.2.0"}, "time": "2025-03-10T20:41:33+00:00"}, {"name": "codeception/lib-innerbrowser", "version": "3.1.3", "source": {"type": "git", "url": "https://github.com/Codeception/lib-innerbrowser.git", "reference": "10482f7e34c0537bf5b87bc82a3d65a1842a8b4f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/lib-innerbrowser/zipball/10482f7e34c0537bf5b87bc82a3d65a1842a8b4f", "reference": "10482f7e34c0537bf5b87bc82a3d65a1842a8b4f", "shasum": ""}, "require": {"codeception/codeception": "^5.0", "codeception/lib-web": "^1.0.1", "ext-dom": "*", "ext-json": "*", "ext-mbstring": "*", "php": "^8.0", "phpunit/phpunit": "^9.5", "symfony/browser-kit": "^4.4.24 || ^5.4 || ^6.0", "symfony/dom-crawler": "^4.4.30 || ^5.4 || ^6.0"}, "require-dev": {"codeception/util-universalframework": "dev-master"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://codegyre.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "Parent library for all Codeception framework modules and PhpBrowser", "homepage": "https://codeception.com/", "keywords": ["codeception"], "support": {"issues": "https://github.com/Codeception/lib-innerbrowser/issues", "source": "https://github.com/Codeception/lib-innerbrowser/tree/3.1.3"}, "time": "2022-10-03T15:33:34+00:00"}, {"name": "codeception/lib-web", "version": "1.0.7", "source": {"type": "git", "url": "https://github.com/Codeception/lib-web.git", "reference": "1444ccc9b1d6721f3ced8703c8f4a9041b80df93"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/lib-web/zipball/1444ccc9b1d6721f3ced8703c8f4a9041b80df93", "reference": "1444ccc9b1d6721f3ced8703c8f4a9041b80df93", "shasum": ""}, "require": {"ext-mbstring": "*", "guzzlehttp/psr7": "^2.0", "php": "^8.1", "phpunit/phpunit": "^9.5 | ^10.0 | ^11.0 | ^12", "symfony/css-selector": ">=4.4.24 <8.0"}, "conflict": {"codeception/codeception": "<5.0.0-alpha3"}, "require-dev": {"php-webdriver/webdriver": "^1.12"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "Library containing files used by module-webdriver and lib-innerbrowser or module-phpbrowser", "homepage": "https://codeception.com/", "keywords": ["codeception"], "support": {"issues": "https://github.com/Codeception/lib-web/issues", "source": "https://github.com/Codeception/lib-web/tree/1.0.7"}, "time": "2025-02-09T12:05:55+00:00"}, {"name": "codeception/module-asserts", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/Codeception/module-asserts.git", "reference": "eb1f7c980423888f3def5116635754ae4a75bd47"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/module-asserts/zipball/eb1f7c980423888f3def5116635754ae4a75bd47", "reference": "eb1f7c980423888f3def5116635754ae4a75bd47", "shasum": ""}, "require": {"codeception/codeception": "*@dev", "codeception/lib-asserts": "^2.2", "php": "^8.2"}, "conflict": {"codeception/codeception": "<5.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "homepage": "https://medium.com/@ganieves"}], "description": "Codeception module containing various assertions", "homepage": "https://codeception.com/", "keywords": ["assertions", "asserts", "codeception"], "support": {"issues": "https://github.com/Codeception/module-asserts/issues", "source": "https://github.com/Codeception/module-asserts/tree/3.2.0"}, "time": "2025-05-02T02:33:11+00:00"}, {"name": "codeception/module-filesystem", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/Codeception/module-filesystem.git", "reference": "0fd78cf941cb72dc2a650c6132c5999c26ad4f9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/module-filesystem/zipball/0fd78cf941cb72dc2a650c6132c5999c26ad4f9a", "reference": "0fd78cf941cb72dc2a650c6132c5999c26ad4f9a", "shasum": ""}, "require": {"codeception/codeception": "*@dev", "php": "^8.0", "symfony/finder": "^4.4 || ^5.4 || ^6.0 || ^7.0"}, "conflict": {"codeception/codeception": "<5.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "Codeception module for testing local filesystem", "homepage": "https://codeception.com/", "keywords": ["codeception", "filesystem"], "support": {"issues": "https://github.com/Codeception/module-filesystem/issues", "source": "https://github.com/Codeception/module-filesystem/tree/3.0.1"}, "time": "2023-12-08T19:23:28+00:00"}, {"name": "codeception/module-yii2", "version": "1.1.12", "source": {"type": "git", "url": "https://github.com/Codeception/module-yii2.git", "reference": "1ebe6bc2a7f307a6c246026a905612a40ef64859"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/module-yii2/zipball/1ebe6bc2a7f307a6c246026a905612a40ef64859", "reference": "1ebe6bc2a7f307a6c246026a905612a40ef64859", "shasum": ""}, "require": {"codeception/codeception": "^5.0.8", "codeception/lib-innerbrowser": "^3.0 | ^4.0", "php": "^8.0"}, "require-dev": {"codeception/module-asserts": ">= 3.0", "codeception/module-filesystem": "> 3.0", "codeception/verify": "^3.0", "codemix/yii2-localeurls": "^1.7", "phpstan/phpstan": "^1.10", "yiisoft/yii2": "dev-master", "yiisoft/yii2-app-advanced": "dev-master"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "Codeception module for Yii2 framework", "homepage": "https://codeception.com/", "keywords": ["codeception", "yii2"], "support": {"issues": "https://github.com/Codeception/module-yii2/issues", "source": "https://github.com/Codeception/module-yii2/tree/1.1.12"}, "time": "2024-12-09T14:34:26+00:00"}, {"name": "codeception/stub", "version": "4.1.4", "source": {"type": "git", "url": "https://github.com/Codeception/Stub.git", "reference": "6ce453073a0c220b254dd7f4383645615e4071c3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Stub/zipball/6ce453073a0c220b254dd7f4383645615e4071c3", "reference": "6ce453073a0c220b254dd7f4383645615e4071c3", "shasum": ""}, "require": {"php": "^7.4 | ^8.0", "phpunit/phpunit": "^8.4 | ^9.0 | ^10.0 | ^11 | ^12"}, "conflict": {"codeception/codeception": "<5.0.6"}, "require-dev": {"consolidation/robo": "^3.0"}, "type": "library", "autoload": {"psr-4": {"Codeception\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Flexible Stub wrapper for PHPUnit's Mock Builder", "support": {"issues": "https://github.com/Codeception/Stub/issues", "source": "https://github.com/Codeception/Stub/tree/4.1.4"}, "time": "2025-02-14T06:56:33+00:00"}, {"name": "codeception/verify", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/Codeception/Verify.git", "reference": "25b84a96f0fe7dcf28e8021f02b57643b751a707"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Verify/zipball/25b84a96f0fe7dcf28e8021f02b57643b751a707", "reference": "25b84a96f0fe7dcf28e8021f02b57643b751a707", "shasum": ""}, "require": {"ext-dom": "*", "php": "^7.4 || ^8.0", "phpunit/phpunit": "^9.5 | ^10.0"}, "type": "library", "autoload": {"files": ["src/Codeception/bootstrap.php"], "psr-4": {"Codeception\\": "src\\Codeception"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://medium.com/@ganieves"}], "description": "BDD assertion library for PHPUnit", "support": {"issues": "https://github.com/Codeception/Verify/issues", "source": "https://github.com/Codeception/Verify/tree/3.0.0"}, "time": "2023-02-09T07:33:00+00:00"}, {"name": "doctrine/instantiator", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^1.2", "phpstan/phpstan": "^1.9.4", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5.27", "vimeo/psalm": "^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/2.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:23:10+00:00"}, {"name": "fakerphp/faker", "version": "v1.24.1", "source": {"type": "git", "url": "https://github.com/FakerPHP/Faker.git", "reference": "e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FakerPHP/Faker/zipball/e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5", "reference": "e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "psr/container": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "conflict": {"fzaninotto/faker": "*"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "doctrine/persistence": "^1.3 || ^2.0", "ext-intl": "*", "phpunit/phpunit": "^9.5.26", "symfony/phpunit-bridge": "^5.4.16"}, "suggest": {"doctrine/orm": "Required to use Faker\\ORM\\Doctrine", "ext-curl": "Required by Faker\\Provider\\Image to download images.", "ext-dom": "Required by Faker\\Provider\\HtmlLorem for generating random HTML.", "ext-iconv": "Required by Faker\\Provider\\ru_RU\\Text::realText() for generating real Russian text.", "ext-mbstring": "Required for multibyte Unicode string functionality."}, "type": "library", "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.24.1"}, "time": "2024-11-21T13:46:39+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2025-03-27T12:30:47+00:00"}, {"name": "masterminds/html5", "version": "2.9.0", "source": {"type": "git", "url": "https://github.com/Masterminds/html5-php.git", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "shasum": ""}, "require": {"ext-dom": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7 || ^8 || ^9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.9.0"}, "time": "2024-03-31T07:05:07+00:00"}, {"name": "nikic/php-parser", "version": "v5.5.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "ae59794362fe85e051a58ad36b289443f57be7a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/ae59794362fe85e051a58ad36b289443f57be7a9", "reference": "ae59794362fe85e051a58ad36b289443f57be7a9", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.5.0"}, "time": "2025-05-31T08:24:38+00:00"}, {"name": "phar-io/manifest", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/54750ef60c58e43759730615a392c31c80e23176", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.4"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:33:53+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpunit/php-code-coverage", "version": "9.2.32", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "85402a822d1ecf1db1096959413d35e1c37cf1a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/85402a822d1ecf1db1096959413d35e1c37cf1a5", "reference": "85402a822d1ecf1db1096959413d35e1c37cf1a5", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.19.1 || ^5.1.0", "php": ">=7.3", "phpunit/php-file-iterator": "^3.0.6", "phpunit/php-text-template": "^2.0.4", "sebastian/code-unit-reverse-lookup": "^2.0.3", "sebastian/complexity": "^2.0.3", "sebastian/environment": "^5.1.5", "sebastian/lines-of-code": "^1.0.4", "sebastian/version": "^3.0.2", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^9.6"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "type": "library", "extra": {"branch-alias": {"dev-main": "9.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.32"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-08-22T04:23:01+00:00"}, {"name": "phpunit/php-file-iterator", "version": "3.0.6", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/3.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2021-12-02T12:48:52+00:00"}, {"name": "phpunit/php-invoker", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/3.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T05:58:55+00:00"}, {"name": "phpunit/php-text-template", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T05:33:50+00:00"}, {"name": "phpunit/php-timer", "version": "5.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebastian<PERSON>mann/php-timer/tree/5.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:16:10+00:00"}, {"name": "phpunit/phpunit", "version": "9.5.28", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "954ca3113a03bf780d22f07bf055d883ee04b65e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/954ca3113a03bf780d22f07bf055d883ee04b65e", "reference": "954ca3113a03bf780d22f07bf055d883ee04b65e", "shasum": ""}, "require": {"doctrine/instantiator": "^1.3.1 || ^2", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.10.1", "phar-io/manifest": "^2.0.3", "phar-io/version": "^3.0.2", "php": ">=7.3", "phpunit/php-code-coverage": "^9.2.13", "phpunit/php-file-iterator": "^3.0.5", "phpunit/php-invoker": "^3.1.1", "phpunit/php-text-template": "^2.0.3", "phpunit/php-timer": "^5.0.2", "sebastian/cli-parser": "^1.0.1", "sebastian/code-unit": "^1.0.6", "sebastian/comparator": "^4.0.8", "sebastian/diff": "^4.0.3", "sebastian/environment": "^5.1.3", "sebastian/exporter": "^4.0.5", "sebastian/global-state": "^5.0.1", "sebastian/object-enumerator": "^4.0.3", "sebastian/resource-operations": "^3.0.3", "sebastian/type": "^3.2", "sebastian/version": "^3.0.2"}, "suggest": {"ext-soap": "*", "ext-xdebug": "*"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "9.5-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/9.5.28"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "time": "2023-01-14T12:32:24+00:00"}, {"name": "psy/psysh", "version": "v0.12.9", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "1b801844becfe648985372cb4b12ad6840245ace"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/1b801844becfe648985372cb4b12ad6840245ace", "reference": "1b801844becfe648985372cb4b12ad6840245ace", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "^5.0 || ^4.0", "php": "^8.0 || ^7.4", "symfony/console": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4", "symfony/var-dumper": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4"}, "conflict": {"symfony/console": "4.4.37 || 5.3.14 || 5.3.15 || 5.4.3 || 5.4.4 || 6.0.3 || 6.0.4"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well."}, "bin": ["bin/psysh"], "type": "library", "extra": {"bamarni-bin": {"bin-links": false, "forward-command": false}, "branch-alias": {"dev-main": "0.12.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.9"}, "time": "2025-06-23T02:35:06+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "sebastian/cli-parser", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "2b56bea83a09de3ac06bb18b92f068e60cc6f50b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/2b56bea83a09de3ac06bb18b92f068e60cc6f50b", "reference": "2b56bea83a09de3ac06bb18b92f068e60cc6f50b", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/1.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:27:43+00:00"}, {"name": "sebastian/code-unit", "version": "1.0.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/1fc9f64c0927627ef78ba436c9b17d967e68e120", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/1.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:08:54+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T05:30:19+00:00"}, {"name": "sebastian/comparator", "version": "4.0.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "fa0f136dd2334583309d32b62544682ee972b51a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/comparator/zipball/fa0f136dd2334583309d32b62544682ee972b51a", "reference": "fa0f136dd2334583309d32b62544682ee972b51a", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/diff": "^4.0", "sebastian/exporter": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/4.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-09-14T12:41:17+00:00"}, {"name": "sebastian/complexity", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "25f207c40d62b8b7aa32f5ab026c53561964053a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/25f207c40d62b8b7aa32f5ab026c53561964053a", "reference": "25f207c40d62b8b7aa32f5ab026c53561964053a", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-22T06:19:30+00:00"}, {"name": "sebastian/diff", "version": "4.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "ba01945089c3a293b01ba9badc29ad55b106b0bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/ba01945089c3a293b01ba9badc29ad55b106b0bc", "reference": "ba01945089c3a293b01ba9badc29ad55b106b0bc", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3", "symfony/process": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/4.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:30:58+00:00"}, {"name": "sebastian/environment", "version": "5.1.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "830c43a844f1f8d5b7a1f6d6076b784454d8b7ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/830c43a844f1f8d5b7a1f6d6076b784454d8b7ed", "reference": "830c43a844f1f8d5b7a1f6d6076b784454d8b7ed", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/5.1.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:03:51+00:00"}, {"name": "sebastian/exporter", "version": "4.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "78c00df8f170e02473b682df15bfcdacc3d32d72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/78c00df8f170e02473b682df15bfcdacc3d32d72", "reference": "78c00df8f170e02473b682df15bfcdacc3d32d72", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/4.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:33:00+00:00"}, {"name": "sebastian/global-state", "version": "5.0.7", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9", "reference": "bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.7"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:35:11+00:00"}, {"name": "sebastian/lines-of-code", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "e1e4a170560925c26d424b6a03aed157e7dcc5c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/e1e4a170560925c26d424b6a03aed157e7dcc5c5", "reference": "e1e4a170560925c26d424b6a03aed157e7dcc5c5", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/1.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-22T06:20:34+00:00"}, {"name": "sebastian/object-enumerator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/5c9eeac41b290a3712d88851518825ad78f45c71", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/4.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:12:34+00:00"}, {"name": "sebastian/object-reflector", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:14:26+00:00"}, {"name": "sebastian/recursion-context", "version": "4.0.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1", "reference": "e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/4.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:07:39+00:00"}, {"name": "sebastian/resource-operations", "version": "3.0.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "05d5692a7993ecccd56a03e40cd7e5b09b1d404e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/05d5692a7993ecccd56a03e40cd7e5b09b1d404e", "reference": "05d5692a7993ecccd56a03e40cd7e5b09b1d404e", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/3.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-14T16:00:52+00:00"}, {"name": "sebastian/type", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/type/zipball/75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7", "reference": "75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/3.2.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:13:03+00:00"}, {"name": "sebastian/version", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c6c1022351a901512170118436c764e473f6de8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c6c1022351a901512170118436c764e473f6de8c", "reference": "c6c1022351a901512170118436c764e473f6de8c", "shasum": ""}, "require": {"php": ">=7.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/3.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T06:39:44+00:00"}, {"name": "symfony/browser-kit", "version": "v6.4.19", "source": {"type": "git", "url": "https://github.com/symfony/browser-kit.git", "reference": "ce95f3e3239159e7fa3be7690c6ce95a4714637f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/browser-kit/zipball/ce95f3e3239159e7fa3be7690c6ce95a4714637f", "reference": "ce95f3e3239159e7fa3be7690c6ce95a4714637f", "shasum": ""}, "require": {"php": ">=8.1", "symfony/dom-crawler": "^5.4|^6.0|^7.0"}, "require-dev": {"symfony/css-selector": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Simulates the behavior of a web browser, allowing you to make requests, click on links and submit forms programmatically", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/browser-kit/tree/v6.4.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-14T11:23:16+00:00"}, {"name": "symfony/console", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "9e27aecde8f506ba0fd1d9989620c04a87697101"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/9e27aecde8f506ba0fd1d9989620c04a87697101", "reference": "9e27aecde8f506ba0fd1d9989620c04a87697101", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^7.2"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/dotenv": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/lock": "<6.4", "symfony/process": "<6.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T19:55:54+00:00"}, {"name": "symfony/css-selector", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/601a5ce9aaad7bf10797e3663faefce9e26c24e2", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/dom-crawler", "version": "v6.4.23", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "22210aacb35dbadd772325d759d17bce2374a84d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/22210aacb35dbadd772325d759d17bce2374a84d", "reference": "22210aacb35dbadd772325d759d17bce2374a84d", "shasum": ""}, "require": {"masterminds/html5": "^2.6", "php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"symfony/css-selector": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases DOM navigation for HTML and XML documents", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dom-crawler/tree/v6.4.23"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-13T12:10:00+00:00"}, {"name": "symfony/finder", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "ec2344cf77a48253bbca6939aa3d2477773ea63d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/ec2344cf77a48253bbca6939aa3d2477773ea63d", "reference": "ec2344cf77a48253bbca6939aa3d2477773ea63d", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-30T19:00:26+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/string", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "f3570b8c61ca887a9e2938e85cb6458515d2b125"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/f3570b8c61ca887a9e2938e85cb6458515d2b125", "reference": "f3570b8c61ca887a9e2938e85cb6458515d2b125", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/emoji": "^7.1", "symfony/error-handler": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-20T20:19:01+00:00"}, {"name": "symfony/var-dumper", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "6e209fbe5f5a7b6043baba46fe5735a4b85d0d42"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/6e209fbe5f5a7b6043baba46fe5735a4b85d0d42", "reference": "6e209fbe5f5a7b6043baba46fe5735a4b85d0d42", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<6.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/uid": "^6.4|^7.0", "twig/twig": "^3.12"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T19:55:54+00:00"}, {"name": "symfony/yaml", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "0c3555045a46ab3cd4cc5a69d161225195230edb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/0c3555045a46ab3cd4cc5a69d161225195230edb", "reference": "0c3555045a46ab3cd4cc5a69d161225195230edb", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-03T06:57:57+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.3"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:36:25+00:00"}, {"name": "yiisoft/yii2-faker", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-faker.git", "reference": "8c361657143bfaea58ff7dcc9bf51f1991a46f5d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-faker/zipball/8c361657143bfaea58ff7dcc9bf51f1991a46f5d", "reference": "8c361657143bfaea58ff7dcc9bf51f1991a46f5d", "shasum": ""}, "require": {"fakerphp/faker": "~1.9|~1.10", "yiisoft/yii2": "~2.0.0"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34"}, "type": "yii2-extension", "extra": {"patches": {"phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}, "phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}}, "branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true}, "autoload": {"psr-4": {"yii\\faker\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Fixture generator. The Faker integration for the Yii framework.", "keywords": ["Fixture", "faker", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-faker/issues", "source": "https://github.com/yiisoft/yii2-faker", "wiki": "http://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-faker", "type": "tidelift"}], "time": "2020-11-10T12:27:35+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"kartik-v/yii2-widget-activeform": 20, "kartik-v/yii2-widget-fileinput": 20, "kartik-v/yii2-widget-select2": 20, "kartik-v/yii2-widget-timepicker": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=7.4.0"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}