main > .container {
  padding: 70px 15px 20px;
}

.footer {
  background-color: #f5f5f5;
  font-size: 0.9em;
  height: 60px;
}

.footer > .container {
  padding-right: 15px;
  padding-left: 15px;
}

.not-set {
  color: #c55;
  font-style: italic;
}

/* add sorting icons to gridview sort links */
a.asc:after,
a.desc:after {
  content: "";
  left: 3px;
  display: inline-block;
  width: 0;
  height: 0;
  border: solid 5px transparent;
  margin: 4px 4px 2px 4px;
  background: transparent;
}

a.asc:after {
  border-bottom: solid 7px #212529;
  border-top-width: 0;
}

a.desc:after {
  border-top: solid 7px #212529;
  border-bottom-width: 0;
}

.grid-view th,
.grid-view td:last-child {
  white-space: nowrap;
}

.grid-view .filters input,
.grid-view .filters select {
  min-width: 50px;
}

.hint-block {
  display: block;
  margin-top: 5px;
  color: #999;
}

.error-summary {
  color: #a94442;
  background: #fdf7f7;
  border-left: 3px solid #eed3d7;
  padding: 10px 20px;
  margin: 0 0 15px 0;
}

.icon-class {
  width: 18px;
  height: 18px;
}

/* align the logout "link" (button in form) of the navbar */
.nav li > form > button.logout {
  padding-top: 7px;
  color: rgba(255, 255, 255, 0.5);
}

@media (max-width: 767px) {
  .nav li > form > button.logout {
    display: block;
    text-align: left;
    width: 100%;
    padding: 10px 0;
  }
}

.nav > li > form > button.logout:focus,
.nav > li > form > button.logout:hover {
  text-decoration: none;
  color: rgba(255, 255, 255, 0.75);
}

.nav > li > form > button.logout:focus {
  outline: none;
}

.field-loginform-rememberme.mb-3 {
  margin-bottom: 0 !important;
}

/* Style for active parent menu items */
.nav-sidebar .nav-item.menu-open > .nav-link {
  background-color: rgba(0, 123, 255, 0.1);
  color: #000;
}

/* Style for active menu links */
.nav-sidebar .nav-link.active {
  background-color: #007bff;
  color: #fff !important;
}

/* Ensure icons are visible in active state */
.nav-sidebar .nav-link.active i {
  color: #fff !important;
}

/* Style for parent items when child is active */
.nav-sidebar .nav-item.menu-open > .nav-link.active {
  background-color: #0056b3;
  color: #fff !important;
}

/* Hide treeview by default */
.nav-treeview {
  display: none;
}

/* Show treeview when menu is open */
.menu-open > .nav-treeview {
  display: block;
}

/* Container for child items with proper padding */
.nav-sidebar .nav-treeview {
  margin-left: 1rem;
  position: relative;
  width: calc(100% - 0.2rem); /* Make child menu width smaller */
}

/* Ensure active background stays within bounds */
.nav-sidebar .nav-treeview .nav-link {
  position: relative;
  margin-right: 1rem;
  width: calc(100% - 1rem); /* Adjust link width to match container */
}

/* Optional: Add a subtle left border to visually connect parent and children */
.nav-sidebar .nav-treeview::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  border-left: 2px solid rgba(0, 123, 255, 0.1);
}

/* Force parent to clip child menus inside */
.nav-sidebar,
.nav-sidebar .nav-item {
  overflow: hidden;
}

/* Make sure treeview (child) doesn't break layout */
.nav-sidebar .nav-treeview {
  margin: 0; /* remove margin pushing it outside */
  padding-left: 1rem; /* keep nice indent */
  background-color: rgba(0, 123, 255, 0.05); /* subtle background for child */
  border-radius: 0 0 0.5rem 0.5rem; /* round bottom corners */
}

/* Child links */
.nav-sidebar .nav-treeview .nav-link {
  margin: 0 1rem;
  border-radius: 0.375rem; /* softly rounded child buttons too */
}

.hover-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  /* cursor: pointer; */
}
.hover-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}
