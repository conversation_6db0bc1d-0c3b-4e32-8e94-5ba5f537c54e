<?php

// Checks if the current user has admin (ID: 2) or superUser (ID: 1) role

use common\models\documentTypeVraag;
use common\models\document;

/**
 * Fetches all vragen (questions) linked to a document type with all related data
 *
 * @param int $docId The ID of the document to find vragen for
 * @return array An array of data containing Vraag models and their related information
 */
if (!function_exists("getVragenByDocument")) {
    function getVragenByDocument($docId)
    {
        // Find the document by ID
        $document = \common\models\DocumentType::findOne(['id' => $docId]);

        if (!$document) {
            return [
                'success' => false,
                'message' => 'document not found',
                'document' => null,
                'vragen' => []
            ];
        }

        // Find all documenttypeVraag records for this document with related data
        $documentVragen = \common\models\DocumenttypeVraag::find()
            ->where(['document_id' => $document->id])
            ->orderBy(['vraag_nummer' => SORT_ASC])
            ->with([
                'vraag',
                'vraag.createdBy',
                'vraag.updatedBy',
                'createdBy',
                'updatedBy'
            ])
            ->all();

        // Prepare document data
        $documentData = [
            'id' => $document->id,
            'type' => $document->type,
            // 'created_at' => $document->created_at,
            // 'updated_at' => $document->updated_at,
            // 'created_by' => $document->created_by ? [
            //     'id' => $document->createdBy->id,
            //     'username' => $document->createdBy->username
            // ] : null,
            // 'updated_by' => $document->updated_by ? [
            //     'id' => $document->updatedBy->id,
            //     'username' => $document->updatedBy->username
            // ] : null
        ];

        // Extract and format the Vraag models with their relation data
        $vragen = [];
        foreach ($documentVragen as $documentVraag) {
            $vraag = $documentVraag->vraag;

            $vragen[] = [
                'vraag' => [
                    'id' => $vraag->id,
                    'vraag_nummer' => $documentVraag->vraag_nummer,
                    'vraag' => $vraag->vraag,
                    // 'created_at' => $vraag->created_at,
                    // 'updated_at' => $vraag->updated_at,
                    // 'created_by' => $vraag->created_by ? [
                    //     'id' => $vraag->createdBy->id,
                    //     'username' => $vraag->createdBy->username
                    // ] : null,
                    // 'updated_by' => $vraag->updated_by ? [
                    //     'id' => $vraag->updatedBy->id,
                    //     'username' => $vraag->updatedBy->username
                    // ] : null
                ],
            ];
        }

        return [
            'success' => true,
            'document' => $documentData,
            'vragen' => $vragen,
            'count' => count($vragen)
        ];
    }
}

/**
 * Fetches a document with all its related data including questions, answers, and feedback
 *
 * @param int $documentId The ID of the document to fetch
 * @return array An array containing the document and all its related data
 */
if (!function_exists("getDocument")) {
    function getDocument($documentId)
    {
        // Find the document with its related document
        $document = Document::find()
            ->where(['id' => $documentId])
            ->with([
                'documentType',
                'createdBy',
                'updatedBy',
                'documentAntwoords',
                'documentAntwoords.vraag',
                'documentFeedbacks',
                'documentFeedbacks.createdBy',
                'documentSignatures', // <-- Add this line
                'documentSignatures.user' // If you want user info for signature
            ])
            ->one();

        if (!$document) {
            return [
                'success' => false,
                'message' => 'Document not found',
                'data' => null
            ];
        }

        // Get all questions for this document
        $documentVragen = DocumenttypeVraag::find()
            ->where(['document_id' => $document->documenttype_id])
            ->orderBy(['vraag_nummer' => SORT_ASC])
            ->with(['vraag'])
            ->all();

        // Prepare answers map for quick lookup
        $answersMap = [];
        foreach ($document->documentAntwoords as $antwoord) {
            $answersMap[$antwoord->vraag_id] = $antwoord;
        }

        // Prepare questions and answers data
        $questionsAndAnswers = [];
        foreach ($documentVragen as $documentVraag) {
            $vraag = $documentVraag->vraag;
            $answer = isset($answersMap[$vraag->id]) ? $answersMap[$vraag->id] : null;

            $questionsAndAnswers[] = [
                'question' => [
                    'id' => $vraag->id,
                    'number' => $documentVraag->vraag_nummer,
                    'text' => $vraag->vraag
                ],
                'answer' => $answer ? [
                    'id' => $answer->id,
                    'text' => $answer->antwoord,
                    'created_at' => $answer->created_at,
                    'created_by' => [
                        'id' => $answer->created_by,
                        'username' => $answer->createdBy->username
                    ]
                ] : null
            ];
        }

        // Prepare feedback data
        $feedback = array_map(function ($feedback) {
            return [
                'id' => $feedback->id,
                'comment' => $feedback->comment,
                'created_at' => $feedback->created_at,
                'created_by' => [
                    'id' => $feedback->created_by,
                    'username' => $feedback->createdBy->username
                ]
            ];
        }, $document->documentFeedbacks);

        // Prepare signatures data
        $signatures = array_map(function ($signature) {
            return [
                'id' => $signature->id,
                'signed_at' => $signature->signed_at,
                'signature' => $signature->signature_base64,
                'user' => [
                    'id' => $signature->user->id,
                    'username' => $signature->user->username,
                    'role_id' => $signature->user->role->id,
                    'role' => $signature->user->role->name
                ]
            ];
        }, $document->documentSignatures);

        // Prepare the final response
        $documentData = [
            'id' => $document->id,
            'filename' => $document->filename,
            'document' => [
                'id' => $document->documentType->id,
                'type' => $document->documentType->type
            ],
            'status' => $document->workflowStatus->label,
            'created_at' => $document->created_at,
            'created_by' => [
                'id' => $document->created_by,
                'username' => $document->createdBy->username
            ],
            'updated_at' => $document->updated_at,
            'updated_by' => [
                'id' => $document->updated_by,
                'username' => $document->updatedBy->username
            ],
            'questions_and_answers' => $questionsAndAnswers,
            'feedback' => $feedback,
            'signatures' => $signatures // <-- Add signatures to response
        ];

        return [
            'success' => true,
            'data' => $documentData
        ];
    }
}

if (!function_exists("buildMenuTree")) {
    function buildMenuTree($elements, $parentId = null)
    {
        $branch = [];
        $roleId = Yii::$app->user->identity->role_id ?? null;

        foreach ($elements as $element) {
            if ($element['parent_id'] == $parentId) {

                // Skip if 'only_developers' is set and user is not admin/superadmin
                if ($element['only_developers'] && !isAdminOrSuperAdmin()) {
                    continue;
                }

                // Check visible_to_roles (stored as JSON array)
                if (!empty($element['visible_to_roles'])) {
                    $visibleRoles = json_decode($element['visible_to_roles'], true);

                    // Skip if current role is NOT in visible_to_roles
                    if (is_array($visibleRoles) && !in_array($roleId, $visibleRoles)) {
                        continue;
                    }
                }

                // Build the child branch
                $children = buildMenuTree($elements, $element['id'], $roleId);

                $item = [
                    'label' => $element['label'],
                    'url' => $element['url'] != '#' ? [$element['url']] : '#',
                    'target' => $element['target'] ?? '_self',
                ];

                if ($element['heading']) {
                    $item['header'] = true;
                } else {
                    $item['icon'] = $element['icon'] ?? 'dot';
                    $item['iconType'] = $element['icon_type'] ?? 'lucide';
                }

                if (!empty($children)) {
                    $item['items'] = $children;
                }

                $branch[] = $item;
            }
        }

        return $branch;
    }
}

if (!function_exists("normalizePersonName")) {
    function normalizePersonName($filename)
    {
        $filename = pathinfo($filename, PATHINFO_FILENAME); // remove .pdf

        // Remove uploaded_ prefix if present
        $filename = preg_replace('/^uploaded_/i', '', $filename);

        // Replace _ and - with space
        $filename = str_replace(['_', '-'], ' ', $filename);

        // Remove known date formats (e.g. 10-07-2025 or 2025-07-10)
        $filename = preg_replace('/\b\d{1,2}[-\/]\d{1,2}[-\/]\d{4}\b/', '', $filename);

        // Trim and split into words
        $words = preg_split('/\s+/', trim($filename));

        // Assume the person's name is the first word or first two words (you can adjust this)
        $nameParts = array_slice($words, 0, 1); // change to 2 if needed

        return strtoupper(implode(' ', $nameParts));
    }
}
