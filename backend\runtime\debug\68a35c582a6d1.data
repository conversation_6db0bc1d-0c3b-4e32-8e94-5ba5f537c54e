a:14:{s:6:"config";s:15659:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:3:"FMZ";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:70:{s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\Reclassering\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\Reclassering\vendor/yiisoft/yii2-symfonymailer/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte-widgets/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap4/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte3/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-mpdf/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-dialog/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\Reclassering\vendor/kartik-v/yii2-popover-x/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-builder/src";}}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-editable/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-grid/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-helpers/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-context-menu/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-datecontrol/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-httpclient/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-detail-view/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-date-range/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-dynagrid/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\Reclassering\vendor/yiisoft/yii2-jui";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-label-inplace/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-icons/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-money/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-markdown/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\Reclassering\vendor/kartik-v/yii2-slider";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-tabs-x/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-typeahead/src";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-social/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-touchspin/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-switchinput";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-depdrop/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-colorinput/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:66:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:12:"@kartik/date";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datepicker/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-alert/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-affix";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-widgets/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\Reclassering\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\Reclassering\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-select2/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-activeform/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow/src";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-export/src";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\Reclassering\vendor/cornernote/yii2-workflow-manager/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\Reclassering\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\Reclassering\vendor/yiisoft/yii2-twig/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-debug/src";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\Reclassering\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\Reclassering\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-fileinput/src";}}s:24:"rmrevin/yii2-fontawesome";a:3:{s:4:"name";s:24:"rmrevin/yii2-fontawesome";s:7:"version";s:8:"2.10.3.0";s:5:"alias";a:1:{s:24:"@rmrevin/yii/fontawesome";s:51:"C:\Web\Reclassering\vendor/rmrevin/yii2-fontawesome";}}s:24:"edofre/yii2-fullcalendar";a:3:{s:4:"name";s:24:"edofre/yii2-fullcalendar";s:7:"version";s:8:"1.0.11.0";s:5:"alias";a:1:{s:20:"@edofre/fullcalendar";s:55:"C:\Web\Reclassering\vendor/edofre/yii2-fullcalendar/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/time";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-timepicker/src";}}}}";s:3:"log";s:64904:"a:1:{s:8:"messages";a:100:{i:0;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.120804;i:4;a:0:{}i:5;i:2894040;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.121472;i:4;a:0:{}i:5;i:2999264;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.12148;i:4;a:0:{}i:5;i:2999560;}i:3;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.121807;i:4;a:0:{}i:5;i:3029752;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.122095;i:4;a:0:{}i:5;i:3057224;}i:5;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.123021;i:4;a:0:{}i:5;i:3211840;}i:6;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.12303;i:4;a:0:{}i:5;i:3212480;}i:7;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.128538;i:4;a:0:{}i:5;i:4210136;}i:8;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.137696;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5534232;}i:9;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.137741;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5536512;}i:14;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.144842;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5594328;}i:17;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.14799;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5615048;}i:20;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.155761;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6065816;}i:23;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.172002;i:4;a:0:{}i:5;i:6649680;}i:24;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.175993;i:4;a:0:{}i:5;i:6764320;}i:25;a:6:{i:0;s:21:"Loading module: audit";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.176098;i:4;a:0:{}i:5;i:6764960;}i:26;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.178241;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6900416;}i:29;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.181977;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6912048;}i:32;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.182856;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6911832;}i:35;a:6:{i:0;s:40:"Bootstrap with bedezign\yii2\audit\Audit";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.189202;i:4;a:0:{}i:5;i:7092264;}i:37;a:6:{i:0;s:43:"Route requested: 'role-functionality/index'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.192816;i:4;a:0:{}i:5;i:7217680;}i:38;a:6:{i:0;s:38:"Route to run: role-functionality/index";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.195965;i:4;a:0:{}i:5;i:7407848;}i:39;a:6:{i:0;s:170:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/index', '1', '::1', 0, 'GET', '2025-08-18 18:01:12')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.206379;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7666856;}i:42;a:6:{i:0;s:78:"Running action: backend\controllers\RoleFunctionalityController::actionIndex()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.215415;i:4;a:0:{}i:5;i:7676120;}i:43;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.222722;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"C:\Web\Reclassering\common\models\search\RoleFunctionalitySearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:44:"common\models\search\RoleFunctionalitySearch";s:4:"type";s:2:"->";}}i:5;i:7902456;}i:46;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.225474;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"C:\Web\Reclassering\common\models\search\RoleFunctionalitySearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:44:"common\models\search\RoleFunctionalitySearch";s:4:"type";s:2:"->";}}i:5;i:7915320;}i:49;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.228205;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"C:\Web\Reclassering\common\models\search\RoleFunctionalitySearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:44:"common\models\search\RoleFunctionalitySearch";s:4:"type";s:2:"->";}}i:5;i:7916344;}i:52;a:6:{i:0;s:83:"Rendering view file: C:\Web\Reclassering\backend\views\role-functionality\index.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.237847;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:8372504;}i:53;a:6:{i:0;s:20:"SELECT * FROM `role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.273788;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9339344;}i:56;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.274836;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9355416;}i:59;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.280618;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9367608;}i:62;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.283071;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9370448;}i:65;a:6:{i:0;s:24:"Loading module: gridview";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.300146;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-krajee-base\src\Config.php";s:4:"line";i:313;s:8:"function";s:9:"getModule";s:5:"class";s:15:"yii\base\Module";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1255;s:8:"function";s:9:"getModule";s:5:"class";s:18:"kartik\base\Config";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:824;s:8:"function";s:10:"initModule";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:9842008;}i:66;a:6:{i:0;s:41:"SELECT COUNT(*) FROM `role_functionality`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.324789;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10609000;}i:69;a:6:{i:0;s:43:"SELECT * FROM `role_functionality` LIMIT 20";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.338025;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:22;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11042384;}i:72;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.358082;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11588408;}i:75;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.359377;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11601360;}i:78;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.359987;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11617864;}i:81;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.360679;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11631016;}i:84;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.361196;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11643968;}i:87;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.362312;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11654840;}i:90;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.363145;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11667400;}i:93;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.363762;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11680368;}i:96;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.36429;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11691240;}i:99;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.365862;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11703784;}i:102;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.366597;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11716752;}i:105;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.367101;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11727624;}i:108;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.368582;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11740168;}i:111;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.36938;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11753136;}i:114;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.36995;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11764008;}i:117;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.370666;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11776552;}i:120;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.371354;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11789504;}i:123;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.371893;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11800376;}i:126;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.372443;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11812936;}i:129;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.373036;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11831520;}i:132;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.373463;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11842392;}i:135;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.374085;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11854952;}i:138;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.374735;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11867904;}i:141;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.375384;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11878776;}i:144;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.376495;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11891336;}i:147;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.376948;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11904288;}i:150;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37735;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11915160;}i:153;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.377811;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11927880;}i:156;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37874;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11940832;}i:159;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.379381;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11951704;}i:162;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.379925;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11964264;}i:165;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.38027;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11977216;}i:168;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.380603;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11988088;}i:171;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.382174;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12000648;}i:174;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.382972;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12013600;}i:177;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.383495;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12024472;}i:180;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.385338;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12037032;}i:183;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.387021;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12049984;}i:186;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.387858;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12060856;}i:189;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.388756;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12073416;}i:192;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.39023;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12086368;}i:195;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.391413;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12097240;}i:198;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.393774;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12109800;}i:201;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.395772;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12122752;}i:204;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.397217;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12137720;}i:207;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.398148;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12150280;}i:210;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.399365;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12163232;}i:213;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.400036;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12174104;}i:216;a:6:{i:0;s:71:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.404112;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:12256384;}i:217;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\navbar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.416225;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:12365656;}i:218;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.418782;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12517232;}i:221;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.420189;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12522128;}i:224;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.421003;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12531880;}i:227;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.423159;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12543896;}i:230;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.424351;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12546800;}i:233;a:6:{i:0;s:79:"Rendering view file: C:\Web\Reclassering\common\widgets\views\notifications.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.427862;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:27;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12549384;}i:234;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\common\widgets\views\profile.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.428615;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\ProfileWidget.php";s:4:"line";i:32;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:155;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12558928;}i:235;a:6:{i:0;s:74:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\sidebar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.428929;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:49;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:12522904;}i:236;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\common\widgets\views\sidebar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.429759;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:49;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12568936;}i:237;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.434145;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:12656768;}i:240;a:6:{i:0;s:74:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\content.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.441099;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:52;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:12708280;}i:241;a:6:{i:0;s:82:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\control-sidebar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.444362;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:56;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:12768552;}i:242;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\footer.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.445086;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:12770872;}i:243;a:6:{i:0;s:77:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/index'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.451372;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:12789552;}i:246;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.34037017822266, `memory_max`=13022704 WHERE `id`=2529";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.452591;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:12792368;}}}";s:9:"profiling";s:111927:"a:3:{s:6:"memory";i:13022704;s:4:"time";d:0.34846019744873047;s:8:"messages";a:148:{i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.137755;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5537320;}i:11;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.141068;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5580624;}i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.141117;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5580408;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.144394;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5593040;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.144963;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5595240;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.146537;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5597816;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.148059;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5616088;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.149897;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5618616;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.155817;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6066200;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.156756;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6068568;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.178313;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6901328;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.181927;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6910760;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.18199;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6912960;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.182745;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6914872;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.182868;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6913512;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.184504;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6915368;}i:40;a:6:{i:0;s:170:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/index', '1', '::1', 0, 'GET', '2025-08-18 18:01:12')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.206445;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7668216;}i:41;a:6:{i:0;s:170:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/index', '1', '::1', 0, 'GET', '2025-08-18 18:01:12')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.213053;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7670008;}i:44;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.222788;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"C:\Web\Reclassering\common\models\search\RoleFunctionalitySearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:44:"common\models\search\RoleFunctionalitySearch";s:4:"type";s:2:"->";}}i:5;i:7903744;}i:45;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.22505;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"C:\Web\Reclassering\common\models\search\RoleFunctionalitySearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:44:"common\models\search\RoleFunctionalitySearch";s:4:"type";s:2:"->";}}i:5;i:7913648;}i:47;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.225682;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"C:\Web\Reclassering\common\models\search\RoleFunctionalitySearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:44:"common\models\search\RoleFunctionalitySearch";s:4:"type";s:2:"->";}}i:5;i:7916608;}i:48;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.227689;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"C:\Web\Reclassering\common\models\search\RoleFunctionalitySearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:44:"common\models\search\RoleFunctionalitySearch";s:4:"type";s:2:"->";}}i:5;i:7919288;}i:50;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.228261;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"C:\Web\Reclassering\common\models\search\RoleFunctionalitySearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:44:"common\models\search\RoleFunctionalitySearch";s:4:"type";s:2:"->";}}i:5;i:7917760;}i:51;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.232995;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"C:\Web\Reclassering\common\models\search\RoleFunctionalitySearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:44:"common\models\search\RoleFunctionalitySearch";s:4:"type";s:2:"->";}}i:5;i:7921224;}i:54;a:6:{i:0;s:20:"SELECT * FROM `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.273831;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9341008;}i:55;a:6:{i:0;s:20:"SELECT * FROM `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.274608;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9345064;}i:57;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.274856;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9357080;}i:58;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.280482;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9365568;}i:60;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.280653;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9369272;}i:61;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.282213;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9372184;}i:63;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.283163;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9372240;}i:64;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.286787;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9376064;}i:67;a:6:{i:0;s:41:"SELECT COUNT(*) FROM `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.324845;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10610664;}i:68;a:6:{i:0;s:41:"SELECT COUNT(*) FROM `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.32569;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10612288;}i:70;a:6:{i:0;s:43:"SELECT * FROM `role_functionality` LIMIT 20";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.338066;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:22;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11044048;}i:71;a:6:{i:0;s:43:"SELECT * FROM `role_functionality` LIMIT 20";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.339009;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:22;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11059648;}i:73;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.358156;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11590288;}i:74;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.359113;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11592624;}i:76;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.359392;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11603240;}i:77;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.359808;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11606296;}i:79;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.359999;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11619744;}i:80;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.360362;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11622800;}i:82;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.36069;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11632896;}i:83;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.36102;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11635232;}i:85;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.361208;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11645848;}i:86;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.361651;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11648904;}i:88;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.362334;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11656720;}i:89;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.362825;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11659776;}i:91;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.363158;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11669280;}i:92;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.363574;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11671616;}i:94;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.363793;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11682248;}i:95;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.364163;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11685304;}i:97;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.364302;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11693120;}i:98;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.364763;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11696176;}i:100;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.365909;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11705664;}i:101;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.366429;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11708000;}i:103;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.366609;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11718632;}i:104;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.366982;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11721688;}i:106;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.367113;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11729504;}i:107;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.367646;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11732560;}i:109;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.368631;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11742048;}i:110;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.369159;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11744384;}i:112;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.369395;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11755016;}i:113;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.369749;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11758072;}i:115;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.369962;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11765888;}i:116;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.370293;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11768944;}i:118;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.370678;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11778432;}i:119;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.371174;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11780768;}i:121;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.371366;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11791384;}i:122;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.371771;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11794440;}i:124;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.371902;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11802256;}i:125;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37216;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11805312;}i:127;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.372458;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11814816;}i:128;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.372869;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11817152;}i:130;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.373048;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11833400;}i:131;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.373364;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11836456;}i:133;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.373471;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11844272;}i:134;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.373811;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11847328;}i:136;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.374098;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11856832;}i:137;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37441;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11859168;}i:139;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37477;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11869784;}i:140;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.375219;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11872840;}i:142;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.375419;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11880656;}i:143;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37615;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11883712;}i:145;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.376505;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11893216;}i:146;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.376827;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11895552;}i:148;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.376972;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11906168;}i:149;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37726;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11909224;}i:151;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.377357;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11917040;}i:152;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.377621;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11920096;}i:154;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37782;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11929760;}i:155;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.378115;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11932096;}i:157;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.378791;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11942712;}i:158;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37924;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11945768;}i:160;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.379394;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11953584;}i:161;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.379712;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11956640;}i:163;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.379934;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11966144;}i:164;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.380162;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11968480;}i:166;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.380279;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11979096;}i:167;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.380516;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11982152;}i:169;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.380611;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11989968;}i:170;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.381055;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11993024;}i:172;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.382221;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12002528;}i:173;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.382819;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12004864;}i:175;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.382982;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12015480;}i:176;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.383333;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12018536;}i:178;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.383505;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12026352;}i:179;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.384128;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12029408;}i:181;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.385456;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12038912;}i:182;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.386277;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12041248;}i:184;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.387147;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12051864;}i:185;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.387688;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12054920;}i:187;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.38787;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12062736;}i:188;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.388251;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12065792;}i:190;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.388782;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12075296;}i:191;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.389565;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12077632;}i:193;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.390266;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12088248;}i:194;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.390876;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12091304;}i:196;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.391471;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12099120;}i:197;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.392598;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12102176;}i:199;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.393827;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12111680;}i:200;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.394927;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12114016;}i:202;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.395824;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12124632;}i:203;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.396743;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12127688;}i:205;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.397254;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12139600;}i:206;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.397841;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12142656;}i:208;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.398159;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12152160;}i:209;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.398694;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12154496;}i:211;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.399412;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12165112;}i:212;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.399913;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12168168;}i:214;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.400046;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12175984;}i:215;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.400479;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12179040;}i:219;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.418852;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12519008;}i:220;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.419956;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12520704;}i:222;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.420227;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12524008;}i:223;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.420914;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12527568;}i:225;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.421024;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12532904;}i:226;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.423119;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12541208;}i:228;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.423191;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12545560;}i:229;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.423982;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12548360;}i:231;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.424402;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12547952;}i:232;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.427473;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12551800;}i:238;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.434224;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:12658440;}i:239;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.435696;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:12690944;}i:244;a:6:{i:0;s:77:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/index'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.451414;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:12790680;}i:245;a:6:{i:0;s:77:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/index'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.452235;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:12791832;}i:247;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.34037017822266, `memory_max`=13022704 WHERE `id`=2529";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.452629;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:12793712;}i:248;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.34037017822266, `memory_max`=13022704 WHERE `id`=2529";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.455519;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:12795112;}}}";s:2:"db";s:111156:"a:1:{s:8:"messages";a:146:{i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.141117;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5580408;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.144394;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5593040;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.144963;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5595240;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.146537;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5597816;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.148059;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5616088;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.149897;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5618616;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.155817;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6066200;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.156756;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6068568;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.178313;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6901328;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.181927;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6910760;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.18199;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6912960;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.182745;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6914872;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.182868;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6913512;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.184504;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6915368;}i:40;a:6:{i:0;s:170:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/index', '1', '::1', 0, 'GET', '2025-08-18 18:01:12')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.206445;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7668216;}i:41;a:6:{i:0;s:170:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/index', '1', '::1', 0, 'GET', '2025-08-18 18:01:12')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.213053;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7670008;}i:44;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.222788;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"C:\Web\Reclassering\common\models\search\RoleFunctionalitySearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:44:"common\models\search\RoleFunctionalitySearch";s:4:"type";s:2:"->";}}i:5;i:7903744;}i:45;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.22505;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"C:\Web\Reclassering\common\models\search\RoleFunctionalitySearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:44:"common\models\search\RoleFunctionalitySearch";s:4:"type";s:2:"->";}}i:5;i:7913648;}i:47;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.225682;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"C:\Web\Reclassering\common\models\search\RoleFunctionalitySearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:44:"common\models\search\RoleFunctionalitySearch";s:4:"type";s:2:"->";}}i:5;i:7916608;}i:48;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.227689;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"C:\Web\Reclassering\common\models\search\RoleFunctionalitySearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:44:"common\models\search\RoleFunctionalitySearch";s:4:"type";s:2:"->";}}i:5;i:7919288;}i:50;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.228261;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"C:\Web\Reclassering\common\models\search\RoleFunctionalitySearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:44:"common\models\search\RoleFunctionalitySearch";s:4:"type";s:2:"->";}}i:5;i:7917760;}i:51;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.232995;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"C:\Web\Reclassering\common\models\search\RoleFunctionalitySearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:44:"common\models\search\RoleFunctionalitySearch";s:4:"type";s:2:"->";}}i:5;i:7921224;}i:54;a:6:{i:0;s:20:"SELECT * FROM `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.273831;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9341008;}i:55;a:6:{i:0;s:20:"SELECT * FROM `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.274608;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9345064;}i:57;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.274856;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9357080;}i:58;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.280482;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9365568;}i:60;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.280653;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9369272;}i:61;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.282213;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9372184;}i:63;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.283163;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9372240;}i:64;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.286787;i:4;a:3:{i:0;a:5:{s:4:"file";s:65:"C:\Web\Reclassering\backend\views\role-functionality\_columns.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:27;s:8:"function";s:7:"require";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9376064;}i:67;a:6:{i:0;s:41:"SELECT COUNT(*) FROM `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.324845;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10610664;}i:68;a:6:{i:0;s:41:"SELECT COUNT(*) FROM `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.32569;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10612288;}i:70;a:6:{i:0;s:43:"SELECT * FROM `role_functionality` LIMIT 20";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.338066;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:22;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11044048;}i:71;a:6:{i:0;s:43:"SELECT * FROM `role_functionality` LIMIT 20";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.339009;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\views\role-functionality\index.php";s:4:"line";i:22;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11059648;}i:73;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.358156;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11590288;}i:74;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.359113;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11592624;}i:76;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.359392;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11603240;}i:77;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.359808;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11606296;}i:79;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.359999;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11619744;}i:80;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.360362;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11622800;}i:82;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.36069;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11632896;}i:83;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.36102;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11635232;}i:85;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.361208;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11645848;}i:86;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.361651;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11648904;}i:88;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.362334;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11656720;}i:89;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.362825;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11659776;}i:91;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.363158;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11669280;}i:92;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.363574;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11671616;}i:94;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.363793;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11682248;}i:95;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.364163;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11685304;}i:97;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.364302;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11693120;}i:98;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.364763;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11696176;}i:100;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.365909;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11705664;}i:101;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.366429;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11708000;}i:103;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.366609;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11718632;}i:104;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.366982;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11721688;}i:106;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.367113;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11729504;}i:107;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.367646;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11732560;}i:109;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.368631;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11742048;}i:110;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.369159;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11744384;}i:112;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.369395;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11755016;}i:113;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.369749;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11758072;}i:115;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.369962;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11765888;}i:116;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.370293;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11768944;}i:118;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.370678;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11778432;}i:119;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.371174;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11780768;}i:121;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.371366;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11791384;}i:122;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.371771;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11794440;}i:124;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.371902;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11802256;}i:125;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37216;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11805312;}i:127;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.372458;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11814816;}i:128;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.372869;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11817152;}i:130;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.373048;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11833400;}i:131;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.373364;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11836456;}i:133;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.373471;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11844272;}i:134;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.373811;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11847328;}i:136;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.374098;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11856832;}i:137;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37441;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11859168;}i:139;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37477;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11869784;}i:140;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.375219;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11872840;}i:142;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.375419;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11880656;}i:143;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37615;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11883712;}i:145;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.376505;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11893216;}i:146;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.376827;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11895552;}i:148;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.376972;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11906168;}i:149;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37726;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11909224;}i:151;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.377357;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11917040;}i:152;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.377621;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11920096;}i:154;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37782;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11929760;}i:155;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.378115;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11932096;}i:157;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.378791;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11942712;}i:158;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37924;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11945768;}i:160;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.379394;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11953584;}i:161;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.379712;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11956640;}i:163;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.379934;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11966144;}i:164;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.380162;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11968480;}i:166;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.380279;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11979096;}i:167;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.380516;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11982152;}i:169;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.380611;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11989968;}i:170;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.381055;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11993024;}i:172;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.382221;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12002528;}i:173;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.382819;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12004864;}i:175;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.382982;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12015480;}i:176;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.383333;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12018536;}i:178;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.383505;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12026352;}i:179;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.384128;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12029408;}i:181;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.385456;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12038912;}i:182;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.386277;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12041248;}i:184;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.387147;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12051864;}i:185;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.387688;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12054920;}i:187;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.38787;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12062736;}i:188;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.388251;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12065792;}i:190;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.388782;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12075296;}i:191;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.389565;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12077632;}i:193;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.390266;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12088248;}i:194;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.390876;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12091304;}i:196;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.391471;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12099120;}i:197;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.392598;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12102176;}i:199;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.393827;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12111680;}i:200;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.394927;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12114016;}i:202;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.395824;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12124632;}i:203;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.396743;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12127688;}i:205;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.397254;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12139600;}i:206;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.397841;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12142656;}i:208;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.398159;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12152160;}i:209;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.398694;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12154496;}i:211;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.399412;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12165112;}i:212;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.399913;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12168168;}i:214;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.400046;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12175984;}i:215;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.400479;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:12179040;}i:219;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.418852;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12519008;}i:220;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.419956;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12520704;}i:222;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.420227;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12524008;}i:223;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.420914;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12527568;}i:225;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.421024;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12532904;}i:226;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.423119;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12541208;}i:228;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.423191;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12545560;}i:229;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.423982;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12548360;}i:231;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.424402;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12547952;}i:232;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.427473;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:12551800;}i:238;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.434224;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:12658440;}i:239;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.435696;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:12690944;}i:244;a:6:{i:0;s:77:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/index'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.451414;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:12790680;}i:245;a:6:{i:0;s:77:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/index'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.452235;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:12791832;}i:247;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.34037017822266, `memory_max`=13022704 WHERE `id`=2529";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.452629;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:12793712;}i:248;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.34037017822266, `memory_max`=13022704 WHERE `id`=2529";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.455519;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:12795112;}}}";s:5:"event";s:59549:"a:333:{i:0;a:5:{s:4:"time";d:**********.132991;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:**********.141053;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:**********.157368;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:**********.15741;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:**********.165515;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:5;a:5:{s:4:"time";d:**********.192758;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:**********.199913;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:7;a:5:{s:4:"time";d:**********.199957;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:8;a:5:{s:4:"time";d:**********.204846;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:9;a:5:{s:4:"time";d:**********.213989;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:10;a:5:{s:4:"time";d:**********.214022;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:11;a:5:{s:4:"time";d:**********.215225;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:47:"backend\controllers\RoleFunctionalityController";}i:12;a:5:{s:4:"time";d:**********.21776;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:44:"common\models\search\RoleFunctionalitySearch";}i:13;a:5:{s:4:"time";d:**********.217794;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:14;a:5:{s:4:"time";d:**********.22077;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:44:"common\models\search\RoleFunctionalitySearch";}i:15;a:5:{s:4:"time";d:**********.233138;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:44:"common\models\search\RoleFunctionalitySearch";}i:16;a:5:{s:4:"time";d:**********.237827;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:17;a:5:{s:4:"time";d:**********.273671;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:18;a:5:{s:4:"time";d:**********.274798;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:19;a:5:{s:4:"time";d:**********.287016;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:20;a:5:{s:4:"time";d:**********.287115;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:21;a:5:{s:4:"time";d:**********.287198;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:22;a:5:{s:4:"time";d:**********.287242;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:23;a:5:{s:4:"time";d:**********.287258;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:24;a:5:{s:4:"time";d:**********.287273;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:25;a:5:{s:4:"time";d:**********.287287;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:26;a:5:{s:4:"time";d:**********.295907;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:27;a:5:{s:4:"time";d:**********.29637;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:28;a:5:{s:4:"time";d:**********.296398;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:29;a:5:{s:4:"time";d:**********.304834;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:30;a:5:{s:4:"time";d:**********.30489;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:31;a:5:{s:4:"time";d:**********.304934;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:32;a:5:{s:4:"time";d:**********.305029;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:33;a:5:{s:4:"time";d:**********.323702;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:34;a:5:{s:4:"time";d:**********.32377;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:35;a:5:{s:4:"time";d:**********.323789;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:36;a:5:{s:4:"time";d:**********.323801;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:37;a:5:{s:4:"time";d:**********.323827;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:38;a:5:{s:4:"time";d:**********.325805;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:39;a:5:{s:4:"time";d:**********.325861;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:40;a:5:{s:4:"time";d:**********.325888;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:41;a:5:{s:4:"time";d:**********.325911;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:42;a:5:{s:4:"time";d:**********.325933;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:43;a:5:{s:4:"time";d:**********.325981;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:44;a:5:{s:4:"time";d:**********.326003;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:45;a:5:{s:4:"time";d:**********.32603;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:46;a:5:{s:4:"time";d:**********.326051;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:47;a:5:{s:4:"time";d:**********.32617;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:48;a:5:{s:4:"time";d:**********.326188;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:49;a:5:{s:4:"time";d:**********.326202;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:50;a:5:{s:4:"time";d:**********.326216;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:51;a:5:{s:4:"time";d:**********.326227;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:52;a:5:{s:4:"time";d:**********.326239;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:53;a:5:{s:4:"time";d:**********.326248;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:54;a:5:{s:4:"time";d:**********.326258;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:55;a:5:{s:4:"time";d:**********.32628;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:56;a:5:{s:4:"time";d:**********.326293;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:57;a:5:{s:4:"time";d:**********.326302;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:58;a:5:{s:4:"time";d:**********.326311;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:59;a:5:{s:4:"time";d:**********.32632;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:60;a:5:{s:4:"time";d:**********.326329;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:61;a:5:{s:4:"time";d:**********.326341;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:62;a:5:{s:4:"time";d:**********.326349;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:63;a:5:{s:4:"time";d:**********.326359;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:64;a:5:{s:4:"time";d:**********.326379;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:65;a:5:{s:4:"time";d:**********.326388;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:66;a:5:{s:4:"time";d:**********.326397;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:67;a:5:{s:4:"time";d:**********.326407;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:68;a:5:{s:4:"time";d:**********.326416;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:69;a:5:{s:4:"time";d:**********.326424;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:70;a:5:{s:4:"time";d:**********.326433;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:71;a:5:{s:4:"time";d:**********.326443;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:72;a:5:{s:4:"time";d:**********.326452;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:73;a:5:{s:4:"time";d:**********.326462;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:74;a:5:{s:4:"time";d:**********.32647;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:75;a:5:{s:4:"time";d:**********.326479;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:76;a:5:{s:4:"time";d:**********.32649;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:77;a:5:{s:4:"time";d:**********.326499;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:78;a:5:{s:4:"time";d:**********.330988;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:79;a:5:{s:4:"time";d:**********.331011;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:80;a:5:{s:4:"time";d:**********.331537;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:81;a:5:{s:4:"time";d:**********.331559;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:82;a:5:{s:4:"time";d:**********.331572;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:83;a:5:{s:4:"time";d:**********.331602;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:84;a:5:{s:4:"time";d:**********.333663;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:85;a:5:{s:4:"time";d:**********.336661;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:86;a:5:{s:4:"time";d:**********.337918;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:87;a:5:{s:4:"time";d:**********.339181;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:88;a:5:{s:4:"time";d:**********.339317;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:89;a:5:{s:4:"time";d:**********.339409;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:90;a:5:{s:4:"time";d:**********.339493;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:91;a:5:{s:4:"time";d:**********.339575;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:92;a:5:{s:4:"time";d:**********.339657;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:93;a:5:{s:4:"time";d:**********.339796;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:94;a:5:{s:4:"time";d:**********.33998;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:95;a:5:{s:4:"time";d:**********.340026;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:96;a:5:{s:4:"time";d:**********.340059;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:97;a:5:{s:4:"time";d:**********.340097;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:98;a:5:{s:4:"time";d:**********.340442;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:99;a:5:{s:4:"time";d:**********.340579;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:100;a:5:{s:4:"time";d:**********.34067;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:101;a:5:{s:4:"time";d:**********.340797;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:102;a:5:{s:4:"time";d:**********.340883;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:103;a:5:{s:4:"time";d:**********.340933;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:104;a:5:{s:4:"time";d:**********.34095;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:105;a:5:{s:4:"time";d:**********.340964;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:106;a:5:{s:4:"time";d:**********.340979;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:107;a:5:{s:4:"time";d:**********.340993;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:108;a:5:{s:4:"time";d:**********.341007;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:109;a:5:{s:4:"time";d:**********.341021;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:110;a:5:{s:4:"time";d:**********.341034;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:111;a:5:{s:4:"time";d:**********.341048;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:112;a:5:{s:4:"time";d:**********.341062;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:113;a:5:{s:4:"time";d:**********.341075;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:114;a:5:{s:4:"time";d:**********.341088;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:115;a:5:{s:4:"time";d:**********.341101;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:116;a:5:{s:4:"time";d:**********.341114;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:117;a:5:{s:4:"time";d:**********.341127;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:118;a:5:{s:4:"time";d:**********.34114;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:119;a:5:{s:4:"time";d:**********.341287;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:120;a:5:{s:4:"time";d:**********.349483;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"kartik\select2\Select2";}i:121;a:5:{s:4:"time";d:**********.349646;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"kartik\select2\Select2";}i:122;a:5:{s:4:"time";d:**********.355481;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"kartik\select2\Select2";}i:123;a:5:{s:4:"time";d:**********.356482;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:124;a:5:{s:4:"time";d:**********.359198;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:125;a:5:{s:4:"time";d:**********.359223;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:126;a:5:{s:4:"time";d:**********.359311;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:127;a:5:{s:4:"time";d:**********.359842;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:128;a:5:{s:4:"time";d:**********.359872;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:129;a:5:{s:4:"time";d:**********.359933;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:130;a:5:{s:4:"time";d:**********.360387;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:131;a:5:{s:4:"time";d:**********.360409;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:132;a:5:{s:4:"time";d:**********.360526;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:133;a:5:{s:4:"time";d:**********.360634;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:134;a:5:{s:4:"time";d:**********.361084;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:135;a:5:{s:4:"time";d:**********.3611;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:136;a:5:{s:4:"time";d:**********.361155;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:137;a:5:{s:4:"time";d:**********.361856;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:138;a:5:{s:4:"time";d:**********.361965;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:139;a:5:{s:4:"time";d:**********.362145;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:140;a:5:{s:4:"time";d:**********.362862;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:141;a:5:{s:4:"time";d:**********.362887;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:142;a:5:{s:4:"time";d:**********.363096;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:143;a:5:{s:4:"time";d:**********.363629;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:144;a:5:{s:4:"time";d:**********.363648;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:145;a:5:{s:4:"time";d:**********.363706;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:146;a:5:{s:4:"time";d:**********.364187;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:147;a:5:{s:4:"time";d:**********.364207;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:148;a:5:{s:4:"time";d:**********.364248;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:149;a:5:{s:4:"time";d:**********.364915;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:150;a:5:{s:4:"time";d:**********.365017;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:151;a:5:{s:4:"time";d:**********.365699;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:152;a:5:{s:4:"time";d:**********.366473;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:153;a:5:{s:4:"time";d:**********.366492;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:154;a:5:{s:4:"time";d:**********.36655;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:155;a:5:{s:4:"time";d:**********.367005;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:156;a:5:{s:4:"time";d:**********.367025;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:157;a:5:{s:4:"time";d:**********.367063;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:158;a:5:{s:4:"time";d:**********.367756;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:159;a:5:{s:4:"time";d:**********.367843;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:160;a:5:{s:4:"time";d:**********.368478;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:161;a:5:{s:4:"time";d:**********.369218;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:162;a:5:{s:4:"time";d:**********.369242;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:163;a:5:{s:4:"time";d:**********.369316;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:164;a:5:{s:4:"time";d:**********.369841;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:165;a:5:{s:4:"time";d:**********.369863;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:166;a:5:{s:4:"time";d:**********.369905;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:167;a:5:{s:4:"time";d:**********.370374;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:168;a:5:{s:4:"time";d:**********.370436;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:169;a:5:{s:4:"time";d:**********.370624;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:170;a:5:{s:4:"time";d:**********.371221;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:171;a:5:{s:4:"time";d:**********.371241;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:172;a:5:{s:4:"time";d:**********.3713;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:173;a:5:{s:4:"time";d:**********.371803;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:174;a:5:{s:4:"time";d:**********.371822;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:175;a:5:{s:4:"time";d:**********.371858;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:176;a:5:{s:4:"time";d:**********.372177;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:177;a:5:{s:4:"time";d:**********.372193;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:178;a:5:{s:4:"time";d:**********.372393;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:179;a:5:{s:4:"time";d:**********.372909;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:180;a:5:{s:4:"time";d:**********.372928;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:181;a:5:{s:4:"time";d:**********.372988;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:182;a:5:{s:4:"time";d:**********.373384;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:183;a:5:{s:4:"time";d:**********.3734;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:184;a:5:{s:4:"time";d:**********.37343;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:185;a:5:{s:4:"time";d:**********.373845;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:186;a:5:{s:4:"time";d:**********.373865;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:187;a:5:{s:4:"time";d:**********.374047;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:188;a:5:{s:4:"time";d:**********.374499;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:189;a:5:{s:4:"time";d:**********.37453;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:190;a:5:{s:4:"time";d:**********.374646;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:191;a:5:{s:4:"time";d:**********.375257;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:192;a:5:{s:4:"time";d:**********.375281;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:193;a:5:{s:4:"time";d:**********.375328;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:194;a:5:{s:4:"time";d:**********.376209;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:195;a:5:{s:4:"time";d:**********.37626;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:196;a:5:{s:4:"time";d:**********.37646;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:197;a:5:{s:4:"time";d:**********.37686;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:198;a:5:{s:4:"time";d:**********.376874;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:199;a:5:{s:4:"time";d:**********.376916;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:200;a:5:{s:4:"time";d:**********.377278;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:201;a:5:{s:4:"time";d:**********.377293;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:202;a:5:{s:4:"time";d:**********.377322;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:203;a:5:{s:4:"time";d:**********.377635;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:204;a:5:{s:4:"time";d:**********.377647;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:205;a:5:{s:4:"time";d:**********.377779;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:206;a:5:{s:4:"time";d:**********.3782;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:207;a:5:{s:4:"time";d:**********.378266;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:208;a:5:{s:4:"time";d:**********.378527;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:209;a:5:{s:4:"time";d:**********.379271;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:210;a:5:{s:4:"time";d:**********.379294;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:211;a:5:{s:4:"time";d:**********.379337;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:212;a:5:{s:4:"time";d:**********.379736;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:213;a:5:{s:4:"time";d:**********.379754;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:214;a:5:{s:4:"time";d:**********.379892;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:215;a:5:{s:4:"time";d:**********.380187;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:216;a:5:{s:4:"time";d:**********.380199;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:217;a:5:{s:4:"time";d:**********.38024;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:218;a:5:{s:4:"time";d:**********.380533;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:219;a:5:{s:4:"time";d:**********.380548;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:220;a:5:{s:4:"time";d:**********.380576;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:221;a:5:{s:4:"time";d:**********.381173;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:222;a:5:{s:4:"time";d:**********.381259;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:223;a:5:{s:4:"time";d:**********.381986;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:224;a:5:{s:4:"time";d:**********.382863;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:225;a:5:{s:4:"time";d:**********.382877;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:226;a:5:{s:4:"time";d:**********.382929;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:227;a:5:{s:4:"time";d:**********.383391;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:228;a:5:{s:4:"time";d:**********.383414;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:229;a:5:{s:4:"time";d:**********.383455;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:230;a:5:{s:4:"time";d:**********.384268;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:231;a:5:{s:4:"time";d:**********.384364;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:232;a:5:{s:4:"time";d:**********.385156;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:233;a:5:{s:4:"time";d:**********.386475;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:234;a:5:{s:4:"time";d:**********.386555;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:235;a:5:{s:4:"time";d:**********.386832;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:236;a:5:{s:4:"time";d:**********.387726;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:237;a:5:{s:4:"time";d:**********.387756;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:238;a:5:{s:4:"time";d:**********.387812;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:239;a:5:{s:4:"time";d:**********.388273;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:240;a:5:{s:4:"time";d:**********.388292;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:241;a:5:{s:4:"time";d:**********.388664;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:242;a:5:{s:4:"time";d:**********.389726;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:243;a:5:{s:4:"time";d:**********.3898;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:244;a:5:{s:4:"time";d:**********.390058;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:245;a:5:{s:4:"time";d:**********.390976;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:246;a:5:{s:4:"time";d:**********.391043;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:247;a:5:{s:4:"time";d:**********.391181;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:248;a:5:{s:4:"time";d:**********.392758;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:249;a:5:{s:4:"time";d:**********.392858;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:250;a:5:{s:4:"time";d:**********.393581;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:251;a:5:{s:4:"time";d:**********.395214;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:252;a:5:{s:4:"time";d:**********.395296;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:253;a:5:{s:4:"time";d:**********.395582;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:254;a:5:{s:4:"time";d:**********.396841;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:255;a:5:{s:4:"time";d:**********.396914;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:256;a:5:{s:4:"time";d:**********.39707;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:257;a:5:{s:4:"time";d:**********.397886;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:258;a:5:{s:4:"time";d:**********.397914;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:259;a:5:{s:4:"time";d:**********.398104;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:260;a:5:{s:4:"time";d:**********.398866;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:261;a:5:{s:4:"time";d:**********.39894;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:262;a:5:{s:4:"time";d:**********.399198;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:263;a:5:{s:4:"time";d:**********.399939;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:264;a:5:{s:4:"time";d:**********.399959;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:265;a:5:{s:4:"time";d:**********.399997;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:266;a:5:{s:4:"time";d:**********.400496;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:267;a:5:{s:4:"time";d:**********.400512;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:268;a:5:{s:4:"time";d:**********.401115;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:269;a:5:{s:4:"time";d:**********.401122;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:270;a:5:{s:4:"time";d:**********.401127;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:271;a:5:{s:4:"time";d:**********.401189;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:272;a:5:{s:4:"time";d:**********.401478;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:273;a:5:{s:4:"time";d:**********.401484;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:274;a:5:{s:4:"time";d:**********.401823;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:275;a:5:{s:4:"time";d:**********.403401;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:276;a:5:{s:4:"time";d:**********.403501;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:277;a:5:{s:4:"time";d:**********.403696;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:278;a:5:{s:4:"time";d:**********.404047;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:279;a:5:{s:4:"time";d:**********.415678;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:280;a:5:{s:4:"time";d:**********.415878;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:281;a:5:{s:4:"time";d:**********.416169;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:282;a:5:{s:4:"time";d:**********.417555;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:283;a:5:{s:4:"time";d:**********.417578;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:284;a:5:{s:4:"time";d:**********.418526;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:285;a:5:{s:4:"time";d:**********.420048;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:286;a:5:{s:4:"time";d:**********.420949;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:287;a:5:{s:4:"time";d:**********.427554;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:288;a:5:{s:4:"time";d:**********.427575;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:289;a:5:{s:4:"time";d:**********.427589;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:290;a:5:{s:4:"time";d:**********.427593;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:291;a:5:{s:4:"time";d:**********.427597;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:292;a:5:{s:4:"time";d:**********.427856;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:293;a:5:{s:4:"time";d:**********.428226;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:294;a:5:{s:4:"time";d:**********.428239;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:295;a:5:{s:4:"time";d:**********.428513;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:296;a:5:{s:4:"time";d:**********.428519;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:297;a:5:{s:4:"time";d:**********.428612;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:298;a:5:{s:4:"time";d:**********.428808;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:299;a:5:{s:4:"time";d:**********.428816;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:300;a:5:{s:4:"time";d:**********.428837;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:301;a:5:{s:4:"time";d:**********.428926;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:302;a:5:{s:4:"time";d:**********.429294;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:303;a:5:{s:4:"time";d:**********.42931;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:304;a:5:{s:4:"time";d:**********.429747;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:305;a:5:{s:4:"time";d:**********.431892;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:306;a:5:{s:4:"time";d:**********.439224;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:307;a:5:{s:4:"time";d:**********.439275;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:308;a:5:{s:4:"time";d:**********.440657;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:309;a:5:{s:4:"time";d:**********.440715;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:310;a:5:{s:4:"time";d:**********.440738;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:311;a:5:{s:4:"time";d:**********.440765;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:312;a:5:{s:4:"time";d:**********.441085;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:313;a:5:{s:4:"time";d:**********.443468;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:314;a:5:{s:4:"time";d:**********.443499;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:315;a:5:{s:4:"time";d:**********.443682;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:316;a:5:{s:4:"time";d:**********.443904;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:317;a:5:{s:4:"time";d:**********.444348;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:318;a:5:{s:4:"time";d:**********.444785;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:319;a:5:{s:4:"time";d:**********.445075;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:320;a:5:{s:4:"time";d:**********.445436;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:321;a:5:{s:4:"time";d:**********.446615;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:322;a:5:{s:4:"time";d:**********.448385;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:323;a:5:{s:4:"time";d:**********.450202;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:324;a:5:{s:4:"time";d:**********.45025;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:47:"backend\controllers\RoleFunctionalityController";}i:325;a:5:{s:4:"time";d:**********.451189;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:326;a:5:{s:4:"time";d:**********.452278;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:327;a:5:{s:4:"time";d:**********.4524;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:328;a:5:{s:4:"time";d:**********.455568;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:329;a:5:{s:4:"time";d:**********.455581;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:330;a:5:{s:4:"time";d:**********.455591;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:331;a:5:{s:4:"time";d:**********.45615;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:332;a:5:{s:4:"time";d:**********.456557;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:92:"a:3:{s:5:"start";d:**********.111956;s:3:"end";d:**********.464141;s:6:"memory";i:13022704;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:333:"a:3:{s:8:"messages";a:1:{i:36;a:6:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.192788;i:4;a:0:{}i:5;i:7217760;}}s:5:"route";s:24:"role-functionality/index";s:6:"action";s:62:"backend\controllers\RoleFunctionalityController::actionIndex()";}";s:7:"request";s:10916:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:17:{s:12:"content-type";s:0:"";s:14:"content-length";s:1:"0";s:14:"sec-fetch-dest";s:8:"document";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-site";s:11:"same-origin";s:25:"upgrade-insecure-requests";s:1:"1";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:65:""Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:7:"referer";s:82:"http://localhost:8005/backoffice/index.php?r=role-functionality%2Froutes&role_id=4";s:4:"host";s:14:"localhost:8005";s:6:"cookie";s:1053:"_identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; advanced-frontend=3fp8o6megq4gr3o67bnqks3c7n; advanced-backend-fmz=b7df4be0nkp5g9vhkif794pfkf; advanced-backend-test=o4hu6edu0utm57lp4o2h9jjjj9; _identity-backend=946df6687b5fb74a9b0eaa13756e2019f90ea1fbcefd9b8ca4919bcb5352521aa%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; _csrf-backend=0990b5f7dba8d91868f791f8029ebc7434255b71b1a85dc1ee8d4eff22c10b19a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22nqzMYT-ezqxHHZlR_7HRg_j9Y1RWPfxx%22%3B%7D; advanced-frontend-fmz=tkg1mvdevem4bjme5kqtr5uqg2; _csrf-frontend=47bf2bb7806c31ad01969b32126f9d22af283e8fa8d9d341741e6b3b18a1efb5a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22zNp9QwIxMks_UotPrUD9o5WeVZdsiCsS%22%3B%7D";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:10:"connection";s:10:"keep-alive";}s:15:"responseHeaders";a:8:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68a35c582a6d1";s:16:"X-Debug-Duration";s:3:"345";s:12:"X-Debug-Link";s:64:"/backoffice/index.php?r=debug%2Fdefault%2Fview&tag=68a35c582a6d1";}s:5:"route";s:24:"role-functionality/index";s:6:"action";s:62:"backend\controllers\RoleFunctionalityController::actionIndex()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:103:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-343ecdae-8d6b-4c1e-bd23-da864376d9ff";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Reclassering\Reclassering.config";s:11:"APP_POOL_ID";s:12:"Reclassering";s:17:"ChocolateyInstall";s:25:"C:\ProgramData\chocolatey";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:553:"C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:21:"/backoffice/index.php";s:3:"URL";s:21:"/backoffice/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"8005";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:21:"/backoffice/index.php";s:15:"SCRIPT_FILENAME";s:41:"C:\Web\Reclassering\backend\web\index.php";s:11:"REQUEST_URI";s:50:"/backoffice/index.php?r=role-functionality%2Findex";s:14:"REQUEST_METHOD";s:3:"GET";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"65453";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:28:"r=role-functionality%2Findex";s:15:"PATH_TRANSLATED";s:41:"C:\Web\Reclassering\backend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/2";s:13:"INSTANCE_NAME";s:12:"RECLASSERING";s:11:"INSTANCE_ID";s:1:"2";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\Reclassering\frontend\web";s:12:"CONTENT_TYPE";s:0:"";s:14:"CONTENT_LENGTH";s:1:"0";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:32:"C:\Web\Reclassering\backend\web\";s:12:"APPL_MD_PATH";s:27:"/LM/W3SVC/2/ROOT/backoffice";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:65:""Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:12:"HTTP_REFERER";s:82:"http://localhost:8005/backoffice/index.php?r=role-functionality%2Froutes&role_id=4";s:9:"HTTP_HOST";s:14:"localhost:8005";s:11:"HTTP_COOKIE";s:1053:"_identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; advanced-frontend=3fp8o6megq4gr3o67bnqks3c7n; advanced-backend-fmz=b7df4be0nkp5g9vhkif794pfkf; advanced-backend-test=o4hu6edu0utm57lp4o2h9jjjj9; _identity-backend=946df6687b5fb74a9b0eaa13756e2019f90ea1fbcefd9b8ca4919bcb5352521aa%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; _csrf-backend=0990b5f7dba8d91868f791f8029ebc7434255b71b1a85dc1ee8d4eff22c10b19a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22nqzMYT-ezqxHHZlR_7HRg_j9Y1RWPfxx%22%3B%7D; advanced-frontend-fmz=tkg1mvdevem4bjme5kqtr5uqg2; _csrf-frontend=47bf2bb7806c31ad01969b32126f9d22af283e8fa8d9d341741e6b3b18a1efb5a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22zNp9QwIxMks_UotPrUD9o5WeVZdsiCsS%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:21:"/backoffice/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.103061;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:1:{s:1:"r";s:24:"role-functionality/index";}s:4:"POST";a:0:{}s:6:"COOKIE";a:9:{s:18:"_identity-frontend";s:158:"ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a:2:{i:0;s:18:"_identity-frontend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:16:"sidebar-collapse";s:5:"false";s:17:"advanced-frontend";s:26:"3fp8o6megq4gr3o67bnqks3c7n";s:20:"advanced-backend-fmz";s:26:"b7df4be0nkp5g9vhkif794pfkf";s:21:"advanced-backend-test";s:26:"o4hu6edu0utm57lp4o2h9jjjj9";s:17:"_identity-backend";s:157:"946df6687b5fb74a9b0eaa13756e2019f90ea1fbcefd9b8ca4919bcb5352521aa:2:{i:0;s:17:"_identity-backend";i:1;s:46:"[1,"RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo",2592000]";}";s:13:"_csrf-backend";s:139:"0990b5f7dba8d91868f791f8029ebc7434255b71b1a85dc1ee8d4eff22c10b19a:2:{i:0;s:13:"_csrf-backend";i:1;s:32:"nqzMYT-ezqxHHZlR_7HRg_j9Y1RWPfxx";}";s:21:"advanced-frontend-fmz";s:26:"tkg1mvdevem4bjme5kqtr5uqg2";s:14:"_csrf-frontend";s:140:"47bf2bb7806c31ad01969b32126f9d22af283e8fa8d9d341741e6b3b18a1efb5a:2:{i:0;s:14:"_csrf-frontend";i:1;s:32:"zNp9QwIxMks_UotPrUD9o5WeVZdsiCsS";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";s:32:"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW";}}";s:4:"user";s:1549:"a:5:{s:2:"id";i:1;s:8:"identity";a:12:{s:2:"id";s:1:"1";s:8:"username";s:11:"'SuperUser'";s:8:"auth_key";s:34:"'R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW'";s:13:"password_hash";s:62:"'$2y$13$uUSLwNI3so2bzilzzZdKJ.C1qmfyTdLRRT1XVP8jYO1c38iE6dfxS'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:17:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"1741788198";s:10:"updated_at";s:10:"1749661338";s:18:"verification_token";s:45:"'oZxacBEp5N7LZAqXc3s1haihOCLK8dLU_1741788198'";s:7:"role_id";s:1:"1";s:12:"access_token";s:66:"'CjYetJZp6PI5vAvKuqm6TDSC_2kYfe_LtOkrjMVtEo4IseTg3J-r-Gp3gMgH-pr7'";}s:10:"attributes";a:12:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:8:"Password";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:4:"Role";}i:11;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:16892:"a:30:{s:31:"yii2ajaxcrud\ajaxcrud\CrudAsset";a:9:{s:10:"sourcePath";s:64:"C:\Web\Reclassering\vendor\biladina\yii2-ajaxcrud-bs4\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a6a94290";s:7:"baseUrl";s:27:"/backoffice/assets/a6a94290";s:7:"depends";a:5:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";i:2;s:35:"yii\bootstrap5\BootstrapPluginAsset";i:3;s:25:"kartik\grid\GridViewAsset";i:4;s:38:"yii2ajaxcrud\ajaxcrud\FontAwesomeAsset";}s:2:"js";a:2:{i:0;s:14:"ModalRemote.js";i:1;s:11:"ajaxcrud.js";}s:3:"css";a:1:{i:0;s:12:"ajaxcrud.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\web\YiiAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\ea09678e";s:7:"baseUrl";s:27:"/backoffice/assets/ea09678e";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:6:"yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:50:"C:\Web\Reclassering/vendor/bower-asset/jquery/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\8bc86ca8";s:7:"baseUrl";s:27:"/backoffice/assets/8bc86ca8";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap5\BootstrapAsset";a:9:{s:10:"sourcePath";s:53:"C:\Web\Reclassering/vendor/bower-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\4ceb4c69";s:7:"baseUrl";s:27:"/backoffice/assets/4ceb4c69";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:1:{s:8:"position";i:1;}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap5\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:53:"C:\Web\Reclassering/vendor/bower-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\4ceb4c69";s:7:"baseUrl";s:27:"/backoffice/assets/4ceb4c69";s:7:"depends";a:1:{i:0;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:25:"kartik\grid\GridViewAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a9ee7db7";s:7:"baseUrl";s:27:"/backoffice/assets/a9ee7db7";s:7:"depends";a:4:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:22:"yii\grid\GridViewAsset";i:2;s:16:"yii\web\YiiAsset";i:3;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:15:"css/kv-grid.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:25:"kartik\dialog\DialogAsset";a:17:{s:10:"sourcePath";s:58:"C:\Web\Reclassering\vendor\kartik-v\yii2-dialog\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\1f8ca467";s:7:"baseUrl";s:27:"/backoffice/assets/1f8ca467";s:7:"depends";a:1:{i:0;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:12:"js/dialog.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:1:{s:8:"position";i:1;}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:22:"yii\grid\GridViewAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\ea09678e";s:7:"baseUrl";s:27:"/backoffice/assets/ea09678e";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:15:"yii.gridView.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:38:"yii2ajaxcrud\ajaxcrud\FontAwesomeAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/fortawesome/font-awesome";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\713ebcc9";s:7:"baseUrl";s:27:"/backoffice/assets/713ebcc9";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:13:"js/all.min.js";}s:3:"css";a:1:{i:0;s:15:"css/all.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:31:"kartik\grid\CheckboxColumnAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a9ee7db7";s:7:"baseUrl";s:27:"/backoffice/assets/a9ee7db7";s:7:"depends";a:3:{i:0;s:25:"kartik\grid\GridViewAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/kv-grid-checkbox.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:34:"kartik\dialog\DialogBootstrapAsset";a:17:{s:10:"sourcePath";s:58:"C:\Web\Reclassering\vendor\kartik-v\yii2-dialog\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\1f8ca467";s:7:"baseUrl";s:27:"/backoffice/assets/1f8ca467";s:7:"depends";a:4:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";i:3;s:35:"yii\bootstrap5\BootstrapPluginAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap-dialog.js";}s:3:"css";a:1:{i:0;s:28:"css/bootstrap-dialog-bs4.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:1;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:28:"kartik\dialog\DialogYiiAsset";a:17:{s:10:"sourcePath";s:58:"C:\Web\Reclassering\vendor\kartik-v\yii2-dialog\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\1f8ca467";s:7:"baseUrl";s:27:"/backoffice/assets/1f8ca467";s:7:"depends";a:3:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:16:"js/dialog-yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:27:"kartik\grid\GridExportAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a9ee7db7";s:7:"baseUrl";s:27:"/backoffice/assets/a9ee7db7";s:7:"depends";a:3:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:20:"js/kv-grid-export.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:34:"kartik\grid\GridResizeColumnsAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a9ee7db7";s:7:"baseUrl";s:27:"/backoffice/assets/a9ee7db7";s:7:"depends";a:3:{i:0;s:16:"yii\web\YiiAsset";i:1;s:25:"kartik\grid\GridViewAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:29:"js/jquery.resizableColumns.js";}s:3:"css";a:1:{i:0;s:31:"css/jquery.resizableColumns.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:27:"kartik\select2\Select2Asset";a:17:{s:10:"sourcePath";s:47:"C:\Web\Reclassering/vendor/select2/select2/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\37377bae";s:7:"baseUrl";s:27:"/backoffice/assets/37377bae";s:7:"depends";a:2:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:18:"js/select2.full.js";}s:3:"css";a:1:{i:0;s:15:"css/select2.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:34:"kartik\select2\ThemeKrajeeBs5Asset";a:17:{s:10:"sourcePath";s:66:"C:\Web\Reclassering\vendor\kartik-v\yii2-widget-select2\src/assets";s:8:"basePath";s:46:"C:\Web\Reclassering\backend\web\assets\c97520a";s:7:"baseUrl";s:26:"/backoffice/assets/c97520a";s:7:"depends";a:2:{i:0;s:33:"kartik\select2\Select2KrajeeAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:26:"css/select2-krajee-bs5.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:33:"kartik\select2\Select2KrajeeAsset";a:17:{s:10:"sourcePath";s:66:"C:\Web\Reclassering\vendor\kartik-v\yii2-widget-select2\src/assets";s:8:"basePath";s:46:"C:\Web\Reclassering\backend\web\assets\c97520a";s:7:"baseUrl";s:26:"/backoffice/assets/c97520a";s:7:"depends";a:2:{i:0;s:27:"kartik\select2\Select2Asset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:20:"js/select2-krajee.js";}s:3:"css";a:1:{i:0;s:20:"css/select2-addl.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:23:"kartik\base\WidgetAsset";a:17:{s:10:"sourcePath";s:63:"C:\Web\Reclassering\vendor\kartik-v\yii2-krajee-base\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\da243af0";s:7:"baseUrl";s:27:"/backoffice/assets/da243af0";s:7:"depends";a:2:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:16:"js/kv-widgets.js";}s:3:"css";a:1:{i:0;s:18:"css/kv-widgets.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:21:"yii\widgets\PjaxAsset";a:9:{s:10:"sourcePath";s:48:"C:\Web\Reclassering/vendor/bower-asset/yii2-pjax";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\38002d64";s:7:"baseUrl";s:27:"/backoffice/assets/38002d64";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:14:"jquery.pjax.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:41:"hail812\adminlte3\assets\FontAwesomeAsset";a:9:{s:10:"sourcePath";s:74:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/plugins/fontawesome-free";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\9a5ee6fe";s:7:"baseUrl";s:27:"/backoffice/assets/9a5ee6fe";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:15:"css/all.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:38:"hail812\adminlte3\assets\AdminLteAsset";a:9:{s:10:"sourcePath";s:54:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\f545690b";s:7:"baseUrl";s:27:"/backoffice/assets/f545690b";s:7:"depends";a:2:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";i:1;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:1:{i:0;s:18:"js/adminlte.min.js";}s:3:"css";a:1:{i:0;s:20:"css/adminlte.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:34:"hail812\adminlte3\assets\BaseAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";N;s:7:"baseUrl";N;s:7:"depends";a:3:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";i:2;s:35:"yii\bootstrap4\BootstrapPluginAsset";}s:2:"js";a:0:{}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap4\BootstrapAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\75eeaf84";s:7:"baseUrl";s:27:"/backoffice/assets/75eeaf84";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap4\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\75eeaf84";s:7:"baseUrl";s:27:"/backoffice/assets/75eeaf84";s:7:"depends";a:2:{i:0;s:19:"yii\web\JqueryAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:36:"hail812\adminlte3\assets\PluginAsset";a:9:{s:10:"sourcePath";s:57:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/plugins";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\834b4d89";s:7:"baseUrl";s:27:"/backoffice/assets/834b4d89";s:7:"depends";a:1:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";}s:2:"js";a:0:{}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:46:"/backoffice/assets/e4a6bb80/control_sidebar.js";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:31:"C:\Web\Reclassering\backend\web";s:7:"baseUrl";s:0:"";s:7:"depends";a:1:{i:0;s:39:"\hail812\adminlte3\assets\AdminLteAsset";}s:2:"js";a:1:{i:0;a:1:{i:0;s:45:"backoffice/assets/e4a6bb80/control_sidebar.js";}}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:39:"\hail812\adminlte3\assets\AdminLteAsset";a:9:{s:10:"sourcePath";s:54:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\f545690b";s:7:"baseUrl";s:27:"/backoffice/assets/f545690b";s:7:"depends";a:2:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";i:1;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:1:{i:0;s:18:"js/adminlte.min.js";}s:3:"css";a:1:{i:0;s:20:"css/adminlte.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:23:"backend\assets\AppAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:31:"C:\Web\Reclassering\backend\web";s:7:"baseUrl";s:11:"/backoffice";s:7:"depends";a:7:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";i:2;s:19:"yii\web\JqueryAsset";i:3;s:16:"yii\jui\JuiAsset";i:4;s:29:"kartik\sortable\SortableAsset";i:5;s:38:"hail812\adminlte3\assets\AdminLteAsset";i:6;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:3:{i:0;s:50:"https://unpkg.com/lucide@latest/dist/umd/lucide.js";i:1;s:10:"js/main.js";i:2;s:75:"https://cdn.jsdelivr.net/npm/sweetalert2@11.7.1/dist/sweetalert2.all.min.js";}s:3:"css";a:1:{i:0;s:12:"css/site.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\jui\JuiAsset";a:9:{s:10:"sourcePath";s:48:"C:\Web\Reclassering/vendor/bower-asset/jquery-ui";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a78fe255";s:7:"baseUrl";s:27:"/backoffice/assets/a78fe255";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:12:"jquery-ui.js";}s:3:"css";a:1:{i:0;s:31:"themes/smoothness/jquery-ui.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"kartik\sortable\SortableAsset";a:17:{s:10:"sourcePath";s:60:"C:\Web\Reclassering\vendor\kartik-v\yii2-sortable\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\3202e5a2";s:7:"baseUrl";s:27:"/backoffice/assets/3202e5a2";s:7:"depends";a:2:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:2:{i:0;s:19:"js/html5sortable.js";i:1;s:23:"js/kv-html5-sortable.js";}s:3:"css";a:1:{i:0;s:25:"css/kv-html5-sortable.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}}";s:7:"summary";a:13:{s:3:"tag";s:13:"68a35c582a6d1";s:3:"url";s:71:"http://localhost:8005/backoffice/index.php?r=role-functionality%2Findex";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:3:"::1";s:4:"time";d:**********.103061;s:10:"statusCode";i:200;s:8:"sqlCount";i:73;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:13022704;s:14:"processingTime";d:0.34846019744873047;}s:10:"exceptions";a:0:{}}