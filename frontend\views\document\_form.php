<?php

use common\models\DocumentType;
use yii\helpers\Html;
use kartik\form\ActiveForm;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $model common\models\Rapport */
/* @var $form yii\widgets\ActiveForm */

$isAjax = false;

if (Yii::$app->request->isAjax) {
    $isAjax = true;
}


// Get document_id from URL and fetch questions
$documentId = Yii::$app->request->get('document_id');

$questions = [];
if ($documentId) {
    $questions = \common\models\DocumenttypeVraag::find()
        ->where(['document_id' => $documentId])
        ->orderBy(['vraag_nummer' => SORT_ASC])
        ->with(['vraag'])
        ->all();

    // Set the document_id in the model
    $model->documenttype_id = $documentId;
}
?>

<div class="rapport-form rounded-2">
    <?php if (!$isAjax) { ?>
        <div class="">
            <h3 class="mb-3"><?= $model->documentType->type ?></h3>
            <?php $form = ActiveForm::begin([
                'id' => 'rapport-form',
            ]); ?>

            <?= Html::activeHiddenInput($model, 'documenttype_id') ?>

            <?= $form->field($model, 'filename')->textInput(['maxlength' => true]) ?>

            <?php if (!empty($questions)): ?>
                <div id="questions-container">
                    <?php foreach ($questions as $docVraag): ?>
                        <div class="form-group mb-3">
                            <label class="control-label required">
                                <?= $docVraag->vraag_nummer ?>. <?= Html::encode($docVraag->vraag->vraag) ?>
                            </label>
                            <?= Html::textInput(
                                "Answers[{$docVraag->vraag_id}]",
                                '',
                                [
                                    'class' => 'form-control',
                                    // 'required' => true,
                                    'data-question-id' => $docVraag->vraag_id
                                ]
                            ) ?>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div class="form-group">
                    <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
                </div>
            <?php else: ?>
                <div class="alert alert-warning">
                    No questions found for this document.
                </div>
            <?php endif; ?>

            <?php ActiveForm::end(); ?>
        </div>
    <?php } else { ?>
        <div>
            <?php $form = ActiveForm::begin([
                'id' => 'rapport-form',
                'options' => [
                    'data-pjax' => true, // Add this to identify it as a Pjax form
                ],
            ]); ?>

            <div class="row">
                <div class="col-md-6">
                    <?= $form->field($model, 'documenttype_id')->widget(Select2::classname(), [
                        'data' => ArrayHelper::map(DocumentType::find()->all(), 'id', 'type'),
                        'options' => [
                            'placeholder' => 'Select a document...',
                            'id' => 'document-select'
                        ],
                        'pluginOptions' => [
                            'allowClear' => true,
                            'dropdownParent' => '#ajaxCrudModal'
                        ]
                    ]); ?>
                </div>
                <div class="col-md-6">
                    <?= $form->field($model, 'filename')->textInput(['maxlength' => true]) ?>
                </div>
            </div>


            <div id="questions-container" style="display:none;">
                <div id="questions-list" class="mb-3">
                    <!-- Questions will be loaded here -->
                </div>
            </div>

            <?php if (!$isAjax) { ?>
                <div class="form-group">
                    <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
                </div>
            <?php } ?>

            <?php ActiveForm::end(); ?>

        </div>
    <?php } ?>
</div>

<?php
$getVragenUrl = Url::to(['document/get-vragen']);

!$isAjax ? $script = <<<JS
$('#rapport-form').on('beforeSubmit', function(e) {
    e.preventDefault();
    
    let form = $(this);
    $.ajax({
        url: form.attr('action'),
        type: 'POST',
        data: form.serialize(),
        success: function(response) {
            if (response.success) {
                Toast.fire({
                    icon: 'success',
                    title: response.message
                });
                
                window.location.href = response.redirectUrl;
            } else {
                Toast.fire({
                    icon: 'error',
                    title: 'Error saving rapport ' + response.error
                });
            }
        },
        error: function(xhr, status, error) {
            alert('Error submitting form: ' + error);
        }
    });
    return false;
});
JS : $script = <<<JS
// Handle AJAX form submission
$('#rapport-form').on('beforeSubmit', function(e) {
    e.preventDefault();
    
    let form = $(this);
    let submitBtn = form.find(':submit');
    submitBtn.prop('disabled', true);
    
    $.ajax({
        url: form.attr('action'),
        type: 'POST',
        data: form.serialize(),
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                Toast.fire({
                    icon: 'success',
                    title: response.message
                });
                
                if (response.forceReload) {
                    $.pjax.reload({
                        container: response.forceReload
                    });
                }
                
                $('#ajaxCrudModal').modal('hide');
            } else {
                Toast.fire({
                    icon: 'error',
                    title: response.message || 'An error occurred'
                });
            }
        },
        error: function(xhr, status, error) {
            Toast.fire({
                icon: 'error',
                title: 'Error submitting form: ' + error
            });
        },
        complete: function() {
            submitBtn.prop('disabled', false);
        }
    });
    return false;
});

// When Select2 value changes
$('#document-select').on('change', function() {
    const docId = $(this).val();
    const questionsContainer = $('#questions-container');
    const questionsList = $('#questions-list');
    
    if (!docId) {
        questionsContainer.hide();
        questionsList.empty();
        return;
    }

    // Fetch questions using AJAX
    $.get('$getVragenUrl', { docId: docId })
        .done(function(response) {
            if (response.success) {
                questionsList.empty();
                
                // Create questions list with input fields
                const questions = response.vragen;
                questions.forEach(function(item) {
                    const vraag = item.vraag;
                    questionsList.append(`
                        <div class="form-group mb-3">
                            <label class="control-label" for="answer-\${vraag.id}">
                                \${vraag.vraag_nummer}. \${vraag.vraag}
                            </label>
                            <input type="text" 
                                   id="answer-\${vraag.id}" 
                                   class="form-control" 
                                   name="Answers[\${vraag.id}]" 
                                   data-question-id="\${vraag.id}">
                        </div>
                    `);
                });
                
                questionsContainer.show();
            } else {
                questionsContainer.hide();
                questionsList.html('<div class="alert alert-warning">No questions found</div>');
            }
        })
        .fail(function() {
            questionsContainer.hide();
            questionsList.html('<div class="alert alert-danger">Error loading questions</div>');
        });
});

// Prevent duplicate form submissions
// let isSubmitting = false;
// $('#rapport-form').on('beforeSubmit', function(e) {
//     if (isSubmitting) {
//         return false;
//     }
    
//     isSubmitting = true;
//     return true;
// });
JS;

$this->registerJs($script);
?>