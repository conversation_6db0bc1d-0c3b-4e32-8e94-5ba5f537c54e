<?php

namespace common\models;

use Yii;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\Expression;

/**
 * This is the model class for table "role_functionality".
 *
 * @property int $id
 * @property int $role_id
 * @property int $functionaliteit_id
 * @property int $active
 * @property string $route
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property User $createdBy
 * @property User $updatedBy
 */
class RoleFunctionality extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'role_functionality';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => new Expression('CURRENT_TIMESTAMP'),
            ],
            [
                'class' => BlameableBehavior::class,
                'createdByAttribute' => 'created_by',
                'updatedByAttribute' => 'updated_by',
                'value' => function () {
                    return Yii::$app->user->id;
                }
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['created_by', 'updated_by'], 'default', 'value' => null],
            [['role_id'], 'required'],
            [['active'], 'default', 'value' => 1],
            [['role_id', 'functionaliteit_id', 'created_by', 'updated_by', 'active'], 'integer'],
            [['route'], 'string', 'max' => 255],
            [['created_at', 'updated_at'], 'safe'],
            [['created_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['created_by' => 'id']],
            [['updated_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['updated_by' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'role_id' => 'Role',
            'functionaliteit_id' => 'Functionaliteit ID',
            'active' => 'Active',
            'route' => 'Route',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[CreatedBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCreatedBy()
    {
        return $this->hasOne(User::class, ['id' => 'created_by']);
    }

    /**
     * Gets query for [[UpdatedBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUpdatedBy()
    {
        return $this->hasOne(User::class, ['id' => 'updated_by']);
    }

    /**
     * Gets query for [[Role]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getRole()
    {
        return $this->hasOne(Role::class, ['id' => 'role_id']);
    }

    /**
     * Gets query for [[Functionality]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getFunctionality()
    {
        return $this->hasOne(Functionality::class, ['id' => 'functionaliteit_id']);
    }

    /**
     * After save or delete, invalidate cache for all users with this role
     */
    public function afterSave($insert, $changedAttributes)
    {
        parent::afterSave($insert, $changedAttributes);
        $this->refreshRoleUsersCache();
    }

    public function afterDelete()
    {
        parent::afterDelete();
        $this->refreshRoleUsersCache();
    }

    private function refreshRoleUsersCache()
    {
        $userIds = (new \yii\db\Query())
            ->select('id')
            ->from('{{%user}}')
            ->where(['role_id' => $this->role_id])
            ->column();

        $helper = new \common\components\AccessHelper();

        foreach ($userIds as $userId) {
            Yii::$app->cache->delete("user_permissions_{$userId}");
            // Rebuild cache immediately by calling getCachedPermissions
            (new \ReflectionClass($helper))
                ->getMethod('getCachedPermissions')
                ->invokeArgs($helper, [$userId, $this->role_id]);
        }
    }
}
