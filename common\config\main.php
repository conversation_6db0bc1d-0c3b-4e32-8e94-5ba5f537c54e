<?php
return [
    'name' => 'FMZ',
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm'   => '@vendor/npm-asset',
    ],
    'timeZone' => 'Europe/London',
    'vendorPath' => dirname(dirname(__DIR__)) . '/vendor',
    'modules' => [
        // 'audit' => [
        //     'class' => \bedezign\yii2\audit\Audit::class,
        // ],
    ],
    'components' => [
        'view' => [
            'renderers' => [
                'twig' => [
                    'class' => 'yii\twig\ViewRenderer',
                    'cachePath' => '@runtime/Twig/cache',
                    'options' => [
                        'debug' => true,
                        'auto_reload' => true,
                        'strict_variables' => false,
                        'autoescape' => 'html',
                        'optimizations' => 0,
                        'trim_blocks' => false, // ⬅️ Important
                        'autoescape' => false,  // ⬅️ Optional: allow raw HTML content
                        'autoescape_strategy' => false,
                        'debug' => true,
                        'autoescape' => false,
                    ],
                    'globals' => [
                        'html' => '\yii\helpers\Html',
                    ],
                ],
            ],
        ],
        'formatter' => [
            'class' => 'yii\i18n\Formatter',
            'defaultTimeZone' => 'Europe/London',
            'timeZone' => 'Europe/London',
        ],
        'cache' => [
            'class' => \yii\caching\FileCache::class,
        ],
        'accessHelper' => [
            'class' => 'common\components\AccessHelper',
        ],
        'routeService' => [
            'class' => 'common\components\RouteService',
        ],
    ],
];
