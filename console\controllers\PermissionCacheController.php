<?php

namespace console\controllers;

use Yii;
use yii\console\Controller;
use common\components\AccessHelper;
use common\models\User;

class PermissionCacheController extends Controller
{
    public function actionShow($userId)
    {
        $cacheKey = "user_permissions_{$userId}";
        $permissions = Yii::$app->cache->get($cacheKey);

        if ($permissions === false) {
            echo "❌ No cache found for user {$userId}\n";
        } else {
            echo "✅ Cached permissions for user {$userId}:\n";
            foreach ($permissions as $route => $allowed) {
                echo "- {$route}: " . ($allowed ? "✔ allowed" : "✖ denied") . "\n";
            }
        }
    }

    public function actionClear($userId)
    {
        $cacheKey = "user_permissions_{$userId}";
        if (Yii::$app->cache->delete($cacheKey)) {
            echo "🗑 Cleared cache for user {$userId}\n";
        } else {
            echo "⚠ Nothing to clear for user {$userId}\n";
        }
    }

    public function actionRebuild($userId)
    {
        $user = User::findOne($userId);
        if (!$user) {
            echo "❌ User not found.\n";
            return;
        }

        $helper = new AccessHelper();
        $permissions = (new \ReflectionClass($helper))
            ->getMethod('getCachedPermissions')
            ->invokeArgs($helper, [$userId, $user->role_id]);

        echo "🔄 Rebuilt cache for user {$userId}:\n";
        foreach ($permissions as $route => $allowed) {
            echo "- {$route}: " . ($allowed ? "✔ allowed" : "✖ denied") . "\n";
        }
    }

    /**
     * Rebuild cache for all users
     * Usage: php yii permission/rebuild-all
     */
    public function actionRebuildAll()
    {
        $users = User::find()->all();
        $helper = new AccessHelper();

        foreach ($users as $user) {
            $permissions = (new \ReflectionClass($helper))
                ->getMethod('getCachedPermissions')
                ->invokeArgs($helper, [$user->id, $user->role_id]);

            echo "🔄 Rebuilt cache for user {$user->id} ({$user->username}): " . count($permissions) . " routes\n";
        }

        echo "✅ Rebuilt cache for all users.\n";
    }

    //////////////////////////////////////////////////

    public function actionTest($userId)
    {
        $user = User::findOne($userId);
        if (!$user) {
            echo "User with ID {$userId} not found.\n";
            return 1;
        }

        $accessHelper = Yii::$app->accessHelper;

        $cacheKey = "user_permissions_{$userId}";

        // Check if cache exists
        $cache = Yii::$app->cache;
        if ($cache->exists($cacheKey)) {
            echo "Cache exists for user {$userId}\n";
        } else {
            echo "Cache does NOT exist for user {$userId}\n";
        }

        // Temporarily override getCachedPermissions to log source
        $permissions = $this->getPermissionsWithSource($accessHelper, $user);

        echo "Permissions loaded:\n";
        foreach ($permissions as $route => $allowed) {
            echo sprintf("%-30s : %s\n", $route, $allowed ? 'YES' : 'NO');
        }

        return 0;
    }

    /**
     * Get all permissions with cache/source info
     */
    private function getPermissionsWithSource(AccessHelper $accessHelper, $user)
    {
        $cacheKey = "user_permissions_{$user->id}";
        $cache = Yii::$app->cache;

        $permissions = $cache->get($cacheKey);
        if ($permissions !== false) {
            echo "(FROM CACHE)\n";
            return $permissions;
        }

        echo "(FROM DATABASE)\n";

        // Build permissions
        $permissions = [];

        // Role permissions
        $roleRoutes = \common\models\RoleFunctionality::find()
            ->select('route')
            ->where(['role_id' => $user->role_id, 'active' => 1])
            ->asArray()
            ->column();

        foreach ($roleRoutes as $route) {
            $permissions[$route] = true;
        }

        // User overrides
        $userPermissions = \common\models\UserPermission::find()
            ->select(['route', 'can_access'])
            ->where(['user_id' => $user->id])
            ->asArray()
            ->all();

        foreach ($userPermissions as $perm) {
            $permissions[$perm['route']] = (bool)$perm['can_access'];
        }

        // Save to cache
        $cache->set($cacheKey, $permissions, 3600);

        return $permissions;
    }
}
