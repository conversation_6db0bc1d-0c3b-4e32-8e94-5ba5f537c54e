a:14:{s:6:"config";s:15659:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:3:"FMZ";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:70:{s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\Reclassering\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\Reclassering\vendor/yiisoft/yii2-symfonymailer/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte-widgets/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap4/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte3/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-mpdf/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-dialog/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\Reclassering\vendor/kartik-v/yii2-popover-x/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-builder/src";}}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-editable/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-grid/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-helpers/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-context-menu/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-datecontrol/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-httpclient/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-detail-view/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-date-range/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-dynagrid/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\Reclassering\vendor/yiisoft/yii2-jui";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-label-inplace/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-icons/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-money/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-markdown/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\Reclassering\vendor/kartik-v/yii2-slider";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-tabs-x/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-typeahead/src";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-social/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-touchspin/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-switchinput";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-depdrop/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-colorinput/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:66:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:12:"@kartik/date";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datepicker/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-alert/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-affix";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-widgets/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\Reclassering\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\Reclassering\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-select2/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-activeform/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow/src";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-export/src";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\Reclassering\vendor/cornernote/yii2-workflow-manager/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\Reclassering\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\Reclassering\vendor/yiisoft/yii2-twig/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-debug/src";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\Reclassering\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\Reclassering\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-fileinput/src";}}s:24:"rmrevin/yii2-fontawesome";a:3:{s:4:"name";s:24:"rmrevin/yii2-fontawesome";s:7:"version";s:8:"2.10.3.0";s:5:"alias";a:1:{s:24:"@rmrevin/yii/fontawesome";s:51:"C:\Web\Reclassering\vendor/rmrevin/yii2-fontawesome";}}s:24:"edofre/yii2-fullcalendar";a:3:{s:4:"name";s:24:"edofre/yii2-fullcalendar";s:7:"version";s:8:"1.0.11.0";s:5:"alias";a:1:{s:20:"@edofre/fullcalendar";s:55:"C:\Web\Reclassering\vendor/edofre/yii2-fullcalendar/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/time";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-timepicker/src";}}}}";s:3:"log";s:55761:"a:1:{s:8:"messages";a:94:{i:0;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.236953;i:4;a:0:{}i:5;i:2906768;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.237542;i:4;a:0:{}i:5;i:3011992;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.237549;i:4;a:0:{}i:5;i:3012288;}i:3;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.237867;i:4;a:0:{}i:5;i:3042480;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.23814;i:4;a:0:{}i:5;i:3069952;}i:5;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.239019;i:4;a:0:{}i:5;i:3224568;}i:6;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.239028;i:4;a:0:{}i:5;i:3225208;}i:7;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.243321;i:4;a:0:{}i:5;i:4222864;}i:8;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.252667;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5546960;}i:9;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.252695;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5549240;}i:14;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.280752;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5607056;}i:17;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.283249;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5627776;}i:20;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.291889;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6078544;}i:23;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.308911;i:4;a:0:{}i:5;i:6662408;}i:24;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.311386;i:4;a:0:{}i:5;i:6777048;}i:25;a:6:{i:0;s:21:"Loading module: audit";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.31141;i:4;a:0:{}i:5;i:6777688;}i:26;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.31369;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6913144;}i:29;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.316735;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6924776;}i:32;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.317528;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6924560;}i:35;a:6:{i:0;s:40:"Bootstrap with bedezign\yii2\audit\Audit";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.32477;i:4;a:0:{}i:5;i:7104992;}i:37;a:6:{i:0;s:44:"Route requested: 'role-functionality/routes'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.327396;i:4;a:0:{}i:5;i:7230408;}i:38;a:6:{i:0;s:39:"Route to run: role-functionality/routes";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.33168;i:4;a:0:{}i:5;i:7420536;}i:39;a:6:{i:0;s:172:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/routes', '1', '::1', 0, 'POST', '2025-08-18 18:07:36')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.342306;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7680568;}i:42;a:6:{i:0;s:79:"Running action: backend\controllers\RoleFunctionalityController::actionRoutes()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.351683;i:4;a:0:{}i:5;i:7689960;}i:43;a:6:{i:0;s:52:"DELETE FROM `role_functionality` WHERE `role_id`='4'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.3574;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:289;s:8:"function";s:9:"deleteAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}i:5;i:7786408;}i:46;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.359834;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7797192;}i:49;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.36169;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7808928;}i:52;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.362621;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7808824;}i:55;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/gedetineerde/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.371556;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8014112;}i:58;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.375058;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8032152;}i:61;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37585;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8043312;}i:64;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.376712;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8076208;}i:67;a:6:{i:0;s:192:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/site/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.381467;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8541568;}i:70;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.386007;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8549080;}i:73;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.386893;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8560168;}i:76;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.387535;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8566128;}i:79;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.389261;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8577416;}i:82;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.391359;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8584928;}i:85;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.39258;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8596016;}i:88;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.393099;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8601336;}i:91;a:6:{i:0;s:215:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/save-signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.395517;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8612624;}i:94;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.40062;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8620136;}i:97;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.401831;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8631224;}i:100;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.402343;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8636544;}i:103;a:6:{i:0;s:204:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view-document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.406632;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8647832;}i:106;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.410385;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8655344;}i:109;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.412269;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8666432;}i:112;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.412889;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8671752;}i:115;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.417897;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8684288;}i:118;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.420267;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8691800;}i:121;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.420779;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8702888;}i:124;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.421125;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8708208;}i:127;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/update', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.422502;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8719464;}i:130;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.425903;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8732608;}i:133;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.426676;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8743696;}i:136;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.427458;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8749016;}i:139;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.430439;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8760272;}i:142;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.433184;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8767784;}i:145;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.433765;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8778872;}i:148;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.434107;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8784192;}i:151;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/getvragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.439689;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8795480;}i:154;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.443105;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8802992;}i:157;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.443702;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8814080;}i:160;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.444149;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8819400;}i:163;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-upload/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.449063;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8830688;}i:166;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.452586;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8838200;}i:169;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.453489;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8849288;}i:172;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.454126;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8854608;}i:175;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/dossiers', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.455682;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8865864;}i:178;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.45825;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8873376;}i:181;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.459495;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8884464;}i:184;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.460188;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8889784;}i:187;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.462275;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8901040;}i:190;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.465932;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8914184;}i:193;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.467324;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8925272;}i:196;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.467929;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8930592;}i:199;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.469527;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8941880;}i:202;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.472827;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8949392;}i:205;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.473738;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8960480;}i:208;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.474387;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8965800;}i:211;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.476157;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8977056;}i:214;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.478721;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8984568;}i:217;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.479732;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8995656;}i:220;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.480524;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9000976;}i:223;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewdocument', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.483269;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9012264;}i:226;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.485456;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9019776;}i:229;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.486247;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9030864;}i:232;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.4868;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9036184;}i:235;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewfile', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.488367;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9047440;}i:238;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.491789;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9054952;}i:241;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.492505;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9066040;}i:244;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.493253;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9071360;}i:247;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/routes'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.497885;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:9107472;}i:250;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.26920080184937, `memory_max`=9161928 WHERE `id`=2540";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.498709;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:9110928;}}}";s:9:"profiling";s:106845:"a:3:{s:6:"memory";i:9161928;s:4:"time";d:0.27266502380371094;s:8:"messages";a:158:{i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.252702;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5550048;}i:11;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.278931;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5593352;}i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.278956;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5593136;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.280692;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5605768;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.280767;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5607968;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.281849;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5610544;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.283269;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5628816;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.284973;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5631344;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.29197;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6078928;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.292717;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6081296;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.313719;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6914056;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.316677;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6923488;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.31675;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6925688;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.317425;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6927600;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.317539;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6926240;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.318972;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6928096;}i:40;a:6:{i:0;s:172:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/routes', '1', '::1', 0, 'POST', '2025-08-18 18:07:36')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.342375;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7681928;}i:41;a:6:{i:0;s:172:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/routes', '1', '::1', 0, 'POST', '2025-08-18 18:07:36')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.348712;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7683720;}i:44;a:6:{i:0;s:52:"DELETE FROM `role_functionality` WHERE `role_id`='4'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.35745;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:289;s:8:"function";s:9:"deleteAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}i:5;i:7787536;}i:45;a:6:{i:0;s:52:"DELETE FROM `role_functionality` WHERE `role_id`='4'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.359704;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:289;s:8:"function";s:9:"deleteAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}i:5;i:7788448;}i:47;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.359849;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7798104;}i:48;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.361484;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7807632;}i:50;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.361732;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7809840;}i:51;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.362515;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7812144;}i:53;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.362634;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7809864;}i:54;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.364292;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7812952;}i:56;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/gedetineerde/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.371584;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8014824;}i:57;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/gedetineerde/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.374239;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8015896;}i:59;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37511;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8034032;}i:60;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.375628;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8037088;}i:62;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.375861;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8045088;}i:63;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37634;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8046840;}i:65;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.376722;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8079368;}i:66;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.377331;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8081144;}i:68;a:6:{i:0;s:192:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/site/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.381496;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8542280;}i:69;a:6:{i:0;s:192:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/site/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.384603;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8543352;}i:71;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.386058;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8550960;}i:72;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.386646;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8554016;}i:74;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.386905;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8561944;}i:75;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.387326;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8563696;}i:77;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.387573;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8568008;}i:78;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.388087;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8569784;}i:80;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.389272;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8578128;}i:81;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.391049;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8579200;}i:83;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.391372;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8586808;}i:84;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.391699;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8589864;}i:86;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.392636;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8597792;}i:87;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.393049;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8599544;}i:89;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.393106;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8603216;}i:90;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.393344;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8604992;}i:92;a:6:{i:0;s:215:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/save-signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.395599;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8613336;}i:93;a:6:{i:0;s:215:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/save-signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.399636;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8614472;}i:95;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.400639;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8622016;}i:96;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.401099;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8625072;}i:98;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.40186;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8633000;}i:99;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.402256;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8634752;}i:101;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.402353;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8638424;}i:102;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.402715;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8640200;}i:104;a:6:{i:0;s:204:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view-document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.406676;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8648544;}i:105;a:6:{i:0;s:204:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view-document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.409256;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8649616;}i:107;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.410437;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8657224;}i:108;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.411993;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8660280;}i:110;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.412283;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8668208;}i:111;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.41275;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8669960;}i:113;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.412905;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8673632;}i:114;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.413364;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8675408;}i:116;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.417926;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8685000;}i:117;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.419926;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8686072;}i:119;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.42028;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8693680;}i:120;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.420607;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8696736;}i:122;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.420789;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8704664;}i:123;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.421077;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8706416;}i:125;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.421131;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8710088;}i:126;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.42139;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8711864;}i:128;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/update', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.422513;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8720176;}i:129;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/update', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.424677;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8726880;}i:131;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.425952;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8734488;}i:132;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.426474;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8737544;}i:134;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.426688;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8745472;}i:135;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.427128;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8747224;}i:137;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.427508;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8750896;}i:138;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.428374;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8752672;}i:140;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.430467;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8760984;}i:141;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.432787;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8762056;}i:143;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.433201;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8769664;}i:144;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.43357;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8772720;}i:146;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.433776;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8780648;}i:147;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.434058;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8782400;}i:149;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.434114;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8786072;}i:150;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.434368;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8787848;}i:152;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/getvragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.43974;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8796192;}i:153;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/getvragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.442756;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8797264;}i:155;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.44312;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8804872;}i:156;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.443481;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8807928;}i:158;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.443724;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8815856;}i:159;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.444085;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8817608;}i:161;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.444157;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8821280;}i:162;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.444519;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8823056;}i:164;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-upload/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.449108;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8831400;}i:165;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-upload/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.451279;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8832472;}i:167;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.452635;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8840080;}i:168;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.453276;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8843136;}i:170;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.453502;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8851064;}i:171;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.453861;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8852816;}i:173;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.454169;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8856488;}i:174;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.454579;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8858264;}i:176;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/dossiers', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.455694;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8866576;}i:177;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/dossiers', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.457901;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8867648;}i:179;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.458263;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8875256;}i:180;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.458634;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8878312;}i:182;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.459539;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8886240;}i:183;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.460141;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8887992;}i:185;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.460195;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8891664;}i:186;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.460492;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8893440;}i:188;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.462325;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8901752;}i:189;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.465221;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8902824;}i:191;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.465959;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8916064;}i:192;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.466822;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8919120;}i:194;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.467351;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8927048;}i:195;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.467849;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8928800;}i:197;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.467938;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8932472;}i:198;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.468241;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8934248;}i:200;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.469537;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8942592;}i:201;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.471636;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8943664;}i:203;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.472876;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8951272;}i:204;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.473505;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8954328;}i:206;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.473752;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8962256;}i:207;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.474138;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8964008;}i:209;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.474436;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8967680;}i:210;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.4751;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8969456;}i:212;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.476168;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8977768;}i:213;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.478154;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8978840;}i:215;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.47874;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8986448;}i:216;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.479273;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8989504;}i:218;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.479758;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8997432;}i:219;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.480401;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8999184;}i:221;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.480541;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9002856;}i:222;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.481151;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9004632;}i:224;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewdocument', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.483291;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9012976;}i:225;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewdocument', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.485068;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9014048;}i:227;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.485474;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9021656;}i:228;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.485848;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9024712;}i:230;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.486268;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9032640;}i:231;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.486702;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9034392;}i:233;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.486815;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9038064;}i:234;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.487102;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9039840;}i:236;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewfile', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.488379;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9048152;}i:237;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewfile', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.490583;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9049224;}i:239;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.491841;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9056832;}i:240;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.492302;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9059888;}i:242;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.492517;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9067816;}i:243;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.492956;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9069568;}i:245;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.493295;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9073240;}i:246;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.49385;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9075016;}i:248;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/routes'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.497973;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:9108600;}i:249;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/routes'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.498586;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:9109752;}i:251;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.26920080184937, `memory_max`=9161928 WHERE `id`=2540";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.498719;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:9112272;}i:252;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.26920080184937, `memory_max`=9161928 WHERE `id`=2540";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.500725;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:9113672;}}}";s:2:"db";s:106075:"a:1:{s:8:"messages";a:156:{i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.278956;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5593136;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.280692;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5605768;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.280767;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5607968;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.281849;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5610544;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.283269;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5628816;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.284973;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5631344;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.29197;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6078928;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.292717;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6081296;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.313719;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6914056;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.316677;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6923488;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.31675;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6925688;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.317425;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6927600;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.317539;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6926240;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.318972;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6928096;}i:40;a:6:{i:0;s:172:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/routes', '1', '::1', 0, 'POST', '2025-08-18 18:07:36')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.342375;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7681928;}i:41;a:6:{i:0;s:172:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/routes', '1', '::1', 0, 'POST', '2025-08-18 18:07:36')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.348712;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7683720;}i:44;a:6:{i:0;s:52:"DELETE FROM `role_functionality` WHERE `role_id`='4'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.35745;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:289;s:8:"function";s:9:"deleteAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}i:5;i:7787536;}i:45;a:6:{i:0;s:52:"DELETE FROM `role_functionality` WHERE `role_id`='4'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.359704;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:289;s:8:"function";s:9:"deleteAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}i:5;i:7788448;}i:47;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.359849;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7798104;}i:48;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.361484;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7807632;}i:50;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.361732;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7809840;}i:51;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.362515;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7812144;}i:53;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.362634;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7809864;}i:54;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.364292;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7812952;}i:56;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/gedetineerde/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.371584;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8014824;}i:57;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/gedetineerde/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.374239;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8015896;}i:59;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37511;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8034032;}i:60;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.375628;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8037088;}i:62;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.375861;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8045088;}i:63;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.37634;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8046840;}i:65;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.376722;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8079368;}i:66;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.377331;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8081144;}i:68;a:6:{i:0;s:192:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/site/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.381496;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8542280;}i:69;a:6:{i:0;s:192:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/site/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.384603;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8543352;}i:71;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.386058;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8550960;}i:72;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.386646;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8554016;}i:74;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.386905;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8561944;}i:75;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.387326;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8563696;}i:77;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.387573;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8568008;}i:78;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.388087;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8569784;}i:80;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.389272;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8578128;}i:81;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.391049;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8579200;}i:83;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.391372;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8586808;}i:84;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.391699;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8589864;}i:86;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.392636;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8597792;}i:87;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.393049;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8599544;}i:89;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.393106;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8603216;}i:90;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.393344;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8604992;}i:92;a:6:{i:0;s:215:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/save-signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.395599;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8613336;}i:93;a:6:{i:0;s:215:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/save-signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.399636;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8614472;}i:95;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.400639;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8622016;}i:96;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.401099;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8625072;}i:98;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.40186;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8633000;}i:99;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.402256;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8634752;}i:101;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.402353;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8638424;}i:102;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.402715;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8640200;}i:104;a:6:{i:0;s:204:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view-document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.406676;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8648544;}i:105;a:6:{i:0;s:204:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view-document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.409256;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8649616;}i:107;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.410437;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8657224;}i:108;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.411993;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8660280;}i:110;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.412283;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8668208;}i:111;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.41275;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8669960;}i:113;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.412905;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8673632;}i:114;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.413364;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8675408;}i:116;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.417926;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8685000;}i:117;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.419926;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8686072;}i:119;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.42028;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8693680;}i:120;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.420607;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8696736;}i:122;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.420789;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8704664;}i:123;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.421077;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8706416;}i:125;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.421131;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8710088;}i:126;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.42139;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8711864;}i:128;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/update', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.422513;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8720176;}i:129;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/update', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.424677;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8726880;}i:131;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.425952;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8734488;}i:132;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.426474;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8737544;}i:134;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.426688;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8745472;}i:135;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.427128;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8747224;}i:137;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.427508;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8750896;}i:138;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.428374;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8752672;}i:140;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.430467;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8760984;}i:141;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.432787;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8762056;}i:143;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.433201;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8769664;}i:144;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.43357;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8772720;}i:146;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.433776;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8780648;}i:147;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.434058;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8782400;}i:149;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.434114;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8786072;}i:150;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.434368;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8787848;}i:152;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/getvragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.43974;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8796192;}i:153;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/getvragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.442756;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8797264;}i:155;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.44312;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8804872;}i:156;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.443481;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8807928;}i:158;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.443724;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8815856;}i:159;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.444085;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8817608;}i:161;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.444157;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8821280;}i:162;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.444519;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8823056;}i:164;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-upload/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.449108;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8831400;}i:165;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-upload/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.451279;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8832472;}i:167;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.452635;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8840080;}i:168;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.453276;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8843136;}i:170;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.453502;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8851064;}i:171;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.453861;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8852816;}i:173;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.454169;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8856488;}i:174;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.454579;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8858264;}i:176;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/dossiers', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.455694;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8866576;}i:177;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/dossiers', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.457901;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8867648;}i:179;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.458263;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8875256;}i:180;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.458634;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8878312;}i:182;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.459539;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8886240;}i:183;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.460141;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8887992;}i:185;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.460195;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8891664;}i:186;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.460492;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8893440;}i:188;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.462325;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8901752;}i:189;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.465221;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8902824;}i:191;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.465959;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8916064;}i:192;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.466822;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8919120;}i:194;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.467351;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8927048;}i:195;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.467849;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8928800;}i:197;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.467938;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8932472;}i:198;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.468241;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8934248;}i:200;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.469537;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8942592;}i:201;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.471636;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8943664;}i:203;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.472876;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8951272;}i:204;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.473505;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8954328;}i:206;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.473752;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8962256;}i:207;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.474138;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8964008;}i:209;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.474436;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8967680;}i:210;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.4751;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8969456;}i:212;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.476168;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8977768;}i:213;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.478154;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8978840;}i:215;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.47874;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8986448;}i:216;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.479273;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8989504;}i:218;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.479758;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8997432;}i:219;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.480401;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:8999184;}i:221;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.480541;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9002856;}i:222;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.481151;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9004632;}i:224;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewdocument', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.483291;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9012976;}i:225;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewdocument', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.485068;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9014048;}i:227;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.485474;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9021656;}i:228;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.485848;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9024712;}i:230;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.486268;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9032640;}i:231;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.486702;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9034392;}i:233;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.486815;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9038064;}i:234;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.487102;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9039840;}i:236;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewfile', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.488379;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9048152;}i:237;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewfile', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.490583;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9049224;}i:239;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.491841;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9056832;}i:240;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.492302;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9059888;}i:242;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.492517;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9067816;}i:243;a:6:{i:0;s:77:"SELECT `route` FROM `role_functionality` WHERE (`role_id`=1) AND (`active`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.492956;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:81;s:8:"function";s:6:"column";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9069568;}i:245;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.493295;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9073240;}i:246;a:6:{i:0;s:69:"SELECT `route`, `can_access` FROM `user_permission` WHERE `user_id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.49385;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:92;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:187;s:8:"function";s:10:"invokeArgs";s:5:"class";s:16:"ReflectionMethod";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:16:"refreshUserCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}}i:5;i:9075016;}i:248;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/routes'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.497973;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:9108600;}i:249;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/routes'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.498586;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:9109752;}i:251;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.26920080184937, `memory_max`=9161928 WHERE `id`=2540";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.498719;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:9112272;}i:252;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.26920080184937, `memory_max`=9161928 WHERE `id`=2540";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.500725;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:9113672;}}}";s:5:"event";s:32622:"a:182:{i:0;a:5:{s:4:"time";d:**********.250899;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:**********.278921;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:**********.293215;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:**********.29327;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:**********.302371;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:5;a:5:{s:4:"time";d:**********.327332;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:**********.334824;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:7;a:5:{s:4:"time";d:**********.334907;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:8;a:5:{s:4:"time";d:**********.340765;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:9;a:5:{s:4:"time";d:**********.350636;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:10;a:5:{s:4:"time";d:**********.350723;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:11;a:5:{s:4:"time";d:**********.351541;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:47:"backend\controllers\RoleFunctionalityController";}i:12;a:5:{s:4:"time";d:**********.354027;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:13;a:5:{s:4:"time";d:**********.35979;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:14;a:5:{s:4:"time";d:**********.364692;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:15;a:5:{s:4:"time";d:**********.370445;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:16;a:5:{s:4:"time";d:**********.370582;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:17;a:5:{s:4:"time";d:**********.37429;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:18;a:5:{s:4:"time";d:**********.37493;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:19;a:5:{s:4:"time";d:**********.375684;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:20;a:5:{s:4:"time";d:**********.375706;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:21;a:5:{s:4:"time";d:**********.375798;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:22;a:5:{s:4:"time";d:**********.376664;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:23;a:5:{s:4:"time";d:**********.381053;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:24;a:5:{s:4:"time";d:**********.381089;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:25;a:5:{s:4:"time";d:**********.381307;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:26;a:5:{s:4:"time";d:**********.381357;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:27;a:5:{s:4:"time";d:**********.384741;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:28;a:5:{s:4:"time";d:**********.38582;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:29;a:5:{s:4:"time";d:**********.386689;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:30;a:5:{s:4:"time";d:**********.386718;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:31;a:5:{s:4:"time";d:**********.38684;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:32;a:5:{s:4:"time";d:**********.387375;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:33;a:5:{s:4:"time";d:**********.389054;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:34;a:5:{s:4:"time";d:**********.389071;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:35;a:5:{s:4:"time";d:**********.389184;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:36;a:5:{s:4:"time";d:**********.389211;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:37;a:5:{s:4:"time";d:**********.391079;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:38;a:5:{s:4:"time";d:**********.391309;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:39;a:5:{s:4:"time";d:**********.391799;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:40;a:5:{s:4:"time";d:**********.391898;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:41;a:5:{s:4:"time";d:**********.392375;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:42;a:5:{s:4:"time";d:**********.393065;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:43;a:5:{s:4:"time";d:**********.394248;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:44;a:5:{s:4:"time";d:**********.394274;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:45;a:5:{s:4:"time";d:**********.394957;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:46;a:5:{s:4:"time";d:**********.395107;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:47;a:5:{s:4:"time";d:**********.399955;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:48;a:5:{s:4:"time";d:**********.400542;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:49;a:5:{s:4:"time";d:**********.401263;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:50;a:5:{s:4:"time";d:**********.401378;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:51;a:5:{s:4:"time";d:**********.401712;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:52;a:5:{s:4:"time";d:**********.402287;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:53;a:5:{s:4:"time";d:**********.405765;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:54;a:5:{s:4:"time";d:**********.405826;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:55;a:5:{s:4:"time";d:**********.406329;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:56;a:5:{s:4:"time";d:**********.406434;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:57;a:5:{s:4:"time";d:**********.409306;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:58;a:5:{s:4:"time";d:**********.4102;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:59;a:5:{s:4:"time";d:**********.412033;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:60;a:5:{s:4:"time";d:**********.412062;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:61;a:5:{s:4:"time";d:**********.412206;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:62;a:5:{s:4:"time";d:**********.412818;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:63;a:5:{s:4:"time";d:**********.417477;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:64;a:5:{s:4:"time";d:**********.417507;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:65;a:5:{s:4:"time";d:**********.417712;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:66;a:5:{s:4:"time";d:**********.417765;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:67;a:5:{s:4:"time";d:**********.419964;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:68;a:5:{s:4:"time";d:**********.420208;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:69;a:5:{s:4:"time";d:**********.420629;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:70;a:5:{s:4:"time";d:**********.420649;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:71;a:5:{s:4:"time";d:**********.420734;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:72;a:5:{s:4:"time";d:**********.421093;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:73;a:5:{s:4:"time";d:**********.422308;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:74;a:5:{s:4:"time";d:**********.422324;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:75;a:5:{s:4:"time";d:**********.422433;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:76;a:5:{s:4:"time";d:**********.422455;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:77;a:5:{s:4:"time";d:**********.424761;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:78;a:5:{s:4:"time";d:**********.425725;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:79;a:5:{s:4:"time";d:**********.426505;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:80;a:5:{s:4:"time";d:**********.426529;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:81;a:5:{s:4:"time";d:**********.42663;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:82;a:5:{s:4:"time";d:**********.427215;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:83;a:5:{s:4:"time";d:**********.429907;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:84;a:5:{s:4:"time";d:**********.429944;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:85;a:5:{s:4:"time";d:**********.43026;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:86;a:5:{s:4:"time";d:**********.430321;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:87;a:5:{s:4:"time";d:**********.43283;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:88;a:5:{s:4:"time";d:**********.433113;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:89;a:5:{s:4:"time";d:**********.433601;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:90;a:5:{s:4:"time";d:**********.433623;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:91;a:5:{s:4:"time";d:**********.433714;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:92;a:5:{s:4:"time";d:**********.434071;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:93;a:5:{s:4:"time";d:**********.438741;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:94;a:5:{s:4:"time";d:**********.438798;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:95;a:5:{s:4:"time";d:**********.439345;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:96;a:5:{s:4:"time";d:**********.439465;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:97;a:5:{s:4:"time";d:**********.442798;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:98;a:5:{s:4:"time";d:**********.443044;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:99;a:5:{s:4:"time";d:**********.443513;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:100;a:5:{s:4:"time";d:**********.443539;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:101;a:5:{s:4:"time";d:**********.443647;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:102;a:5:{s:4:"time";d:**********.444106;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:103;a:5:{s:4:"time";d:**********.448209;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:104;a:5:{s:4:"time";d:**********.448274;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:105;a:5:{s:4:"time";d:**********.448768;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:106;a:5:{s:4:"time";d:**********.44887;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:107;a:5:{s:4:"time";d:**********.451387;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:108;a:5:{s:4:"time";d:**********.452396;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:109;a:5:{s:4:"time";d:**********.453311;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:110;a:5:{s:4:"time";d:**********.453337;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:111;a:5:{s:4:"time";d:**********.45344;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:112;a:5:{s:4:"time";d:**********.453918;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:113;a:5:{s:4:"time";d:**********.455465;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:114;a:5:{s:4:"time";d:**********.455481;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:115;a:5:{s:4:"time";d:**********.455603;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:116;a:5:{s:4:"time";d:**********.455629;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:117;a:5:{s:4:"time";d:**********.457947;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:118;a:5:{s:4:"time";d:**********.458191;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:119;a:5:{s:4:"time";d:**********.45874;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:120;a:5:{s:4:"time";d:**********.458845;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:121;a:5:{s:4:"time";d:**********.459312;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:122;a:5:{s:4:"time";d:**********.460155;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:123;a:5:{s:4:"time";d:**********.461494;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:124;a:5:{s:4:"time";d:**********.461522;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:125;a:5:{s:4:"time";d:**********.461963;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:126;a:5:{s:4:"time";d:**********.462058;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:127;a:5:{s:4:"time";d:**********.465375;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:128;a:5:{s:4:"time";d:**********.465852;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:129;a:5:{s:4:"time";d:**********.466894;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:130;a:5:{s:4:"time";d:**********.466946;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:131;a:5:{s:4:"time";d:**********.467214;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:132;a:5:{s:4:"time";d:**********.467877;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:133;a:5:{s:4:"time";d:**********.469313;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:134;a:5:{s:4:"time";d:**********.469331;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:135;a:5:{s:4:"time";d:**********.469446;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:136;a:5:{s:4:"time";d:**********.469473;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:137;a:5:{s:4:"time";d:**********.471703;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:138;a:5:{s:4:"time";d:**********.472643;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:139;a:5:{s:4:"time";d:**********.473543;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:140;a:5:{s:4:"time";d:**********.47357;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:141;a:5:{s:4:"time";d:**********.473684;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:142;a:5:{s:4:"time";d:**********.474217;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:143;a:5:{s:4:"time";d:**********.475941;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:144;a:5:{s:4:"time";d:**********.475955;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:145;a:5:{s:4:"time";d:**********.476078;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:146;a:5:{s:4:"time";d:**********.476105;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:147;a:5:{s:4:"time";d:**********.478232;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:148;a:5:{s:4:"time";d:**********.478649;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:149;a:5:{s:4:"time";d:**********.479352;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:150;a:5:{s:4:"time";d:**********.479401;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:151;a:5:{s:4:"time";d:**********.479626;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:152;a:5:{s:4:"time";d:**********.480444;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:153;a:5:{s:4:"time";d:**********.482899;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:154;a:5:{s:4:"time";d:**********.482927;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:155;a:5:{s:4:"time";d:**********.483132;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:156;a:5:{s:4:"time";d:**********.483178;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:157;a:5:{s:4:"time";d:**********.485124;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:158;a:5:{s:4:"time";d:**********.485384;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:159;a:5:{s:4:"time";d:**********.485908;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:160;a:5:{s:4:"time";d:**********.48595;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:161;a:5:{s:4:"time";d:**********.486158;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:162;a:5:{s:4:"time";d:**********.486734;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:163;a:5:{s:4:"time";d:**********.488141;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:164;a:5:{s:4:"time";d:**********.488161;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:165;a:5:{s:4:"time";d:**********.48828;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:166;a:5:{s:4:"time";d:**********.488311;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:167;a:5:{s:4:"time";d:**********.49066;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:168;a:5:{s:4:"time";d:**********.491598;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:169;a:5:{s:4:"time";d:**********.492338;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:170;a:5:{s:4:"time";d:**********.492362;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:171;a:5:{s:4:"time";d:**********.492457;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:172;a:5:{s:4:"time";d:**********.493068;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:173;a:5:{s:4:"time";d:**********.495982;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:47:"backend\controllers\RoleFunctionalityController";}i:174;a:5:{s:4:"time";d:**********.497714;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:175;a:5:{s:4:"time";d:**********.498606;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:176;a:5:{s:4:"time";d:**********.498648;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:177;a:5:{s:4:"time";d:**********.500751;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:178;a:5:{s:4:"time";d:**********.500757;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:179;a:5:{s:4:"time";d:**********.500764;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:180;a:5:{s:4:"time";d:**********.501251;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:181;a:5:{s:4:"time";d:**********.501281;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:90:"a:3:{s:5:"start";d:**********.229422;s:3:"end";d:**********.50361;s:6:"memory";i:9208000;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:335:"a:3:{s:8:"messages";a:1:{i:36;a:6:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.327361;i:4;a:0:{}i:5;i:7230488;}}s:5:"route";s:25:"role-functionality/routes";s:6:"action";s:63:"backend\controllers\RoleFunctionalityController::actionRoutes()";}";s:7:"request";s:14243:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:302;s:14:"requestHeaders";a:20:{s:12:"content-type";s:33:"application/x-www-form-urlencoded";s:14:"content-length";s:4:"1115";s:14:"sec-fetch-dest";s:8:"document";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-site";s:11:"same-origin";s:6:"origin";s:21:"http://localhost:8005";s:25:"upgrade-insecure-requests";s:1:"1";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:65:""Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:7:"referer";s:82:"http://localhost:8005/backoffice/index.php?r=role-functionality%2Froutes&role_id=4";s:4:"host";s:14:"localhost:8005";s:6:"cookie";s:1053:"_identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; advanced-frontend=3fp8o6megq4gr3o67bnqks3c7n; advanced-backend-fmz=b7df4be0nkp5g9vhkif794pfkf; advanced-backend-test=o4hu6edu0utm57lp4o2h9jjjj9; _identity-backend=946df6687b5fb74a9b0eaa13756e2019f90ea1fbcefd9b8ca4919bcb5352521aa%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; _csrf-backend=0990b5f7dba8d91868f791f8029ebc7434255b71b1a85dc1ee8d4eff22c10b19a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22nqzMYT-ezqxHHZlR_7HRg_j9Y1RWPfxx%22%3B%7D; advanced-frontend-fmz=tkg1mvdevem4bjme5kqtr5uqg2; _csrf-frontend=47bf2bb7806c31ad01969b32126f9d22af283e8fa8d9d341741e6b3b18a1efb5a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22zNp9QwIxMks_UotPrUD9o5WeVZdsiCsS%22%3B%7D";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:10:"connection";s:10:"keep-alive";s:13:"cache-control";s:9:"max-age=0";}s:15:"responseHeaders";a:9:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:8:"Location";s:82:"http://localhost:8005/backoffice/index.php?r=role-functionality%2Froutes&role_id=4";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68a35dd84babf";s:16:"X-Debug-Duration";s:3:"273";s:12:"X-Debug-Link";s:64:"/backoffice/index.php?r=debug%2Fdefault%2Fview&tag=68a35dd84babf";}s:5:"route";s:25:"role-functionality/routes";s:6:"action";s:63:"backend\controllers\RoleFunctionalityController::actionRoutes()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:33:"application/x-www-form-urlencoded";s:3:"Raw";s:1115:"_csrf-backend=NRbz7LqfI-Wkjf48YayGKXcf7CbBaQ1Zrb5awyjOA95bZ4mh48sOgN78hnQp9up7KCikdKY2Z2D0jwiUeKh7pg%3D%3D&RoleFunctionality%5Brole_id%5D=4&custom_route=gedetineerde&RoleFunctionality%5Broute%5D%5B%5D=%2Fgedetineerde%2Findex&RoleFunctionality%5Broute%5D%5B%5D=%2Fsite%2Findex&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument-signature%2Fcreate&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument-signature%2Fsave-signature&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fview-document&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Findex&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fupdate&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fcreate&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fgetvragen&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument-upload%2Findex&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fdossiers&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fdocument&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fsignature&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fview&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fviewdocument&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fviewfile";s:7:"Decoded";a:3:{s:13:"_csrf-backend";s:88:"NRbz7LqfI-Wkjf48YayGKXcf7CbBaQ1Zrb5awyjOA95bZ4mh48sOgN78hnQp9up7KCikdKY2Z2D0jwiUeKh7pg==";s:17:"RoleFunctionality";a:2:{s:7:"role_id";s:1:"4";s:5:"route";a:16:{i:0;s:19:"/gedetineerde/index";i:1;s:11:"/site/index";i:2;s:26:"/document-signature/create";i:3;s:34:"/document-signature/save-signature";i:4;s:23:"/document/view-document";i:5;s:15:"/document/index";i:6;s:16:"/document/update";i:7;s:16:"/document/create";i:8;s:19:"/document/getvragen";i:9;s:22:"/document-upload/index";i:10;s:18:"/document/dossiers";i:11;s:18:"/document/document";i:12;s:19:"/document/signature";i:13;s:14:"/document/view";i:14;s:22:"/document/viewdocument";i:15;s:18:"/document/viewfile";}}s:12:"custom_route";s:12:"gedetineerde";}}s:6:"SERVER";a:108:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-343ecdae-8d6b-4c1e-bd23-da864376d9ff";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Reclassering\Reclassering.config";s:11:"APP_POOL_ID";s:12:"Reclassering";s:17:"ChocolateyInstall";s:25:"C:\ProgramData\chocolatey";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:553:"C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:21:"/backoffice/index.php";s:3:"URL";s:21:"/backoffice/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"8005";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:21:"/backoffice/index.php";s:15:"SCRIPT_FILENAME";s:41:"C:\Web\Reclassering\backend\web\index.php";s:11:"REQUEST_URI";s:61:"/backoffice/index.php?r=role-functionality%2Froutes&role_id=4";s:14:"REQUEST_METHOD";s:4:"POST";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"65453";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:39:"r=role-functionality%2Froutes&role_id=4";s:15:"PATH_TRANSLATED";s:41:"C:\Web\Reclassering\backend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/2";s:13:"INSTANCE_NAME";s:12:"RECLASSERING";s:11:"INSTANCE_ID";s:1:"2";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\Reclassering\frontend\web";s:12:"CONTENT_TYPE";s:33:"application/x-www-form-urlencoded";s:14:"CONTENT_LENGTH";s:4:"1115";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:32:"C:\Web\Reclassering\backend\web\";s:12:"APPL_MD_PATH";s:27:"/LM/W3SVC/2/ROOT/backoffice";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:11:"HTTP_ORIGIN";s:21:"http://localhost:8005";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:65:""Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:12:"HTTP_REFERER";s:82:"http://localhost:8005/backoffice/index.php?r=role-functionality%2Froutes&role_id=4";s:9:"HTTP_HOST";s:14:"localhost:8005";s:11:"HTTP_COOKIE";s:1053:"_identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; advanced-frontend=3fp8o6megq4gr3o67bnqks3c7n; advanced-backend-fmz=b7df4be0nkp5g9vhkif794pfkf; advanced-backend-test=o4hu6edu0utm57lp4o2h9jjjj9; _identity-backend=946df6687b5fb74a9b0eaa13756e2019f90ea1fbcefd9b8ca4919bcb5352521aa%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; _csrf-backend=0990b5f7dba8d91868f791f8029ebc7434255b71b1a85dc1ee8d4eff22c10b19a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22nqzMYT-ezqxHHZlR_7HRg_j9Y1RWPfxx%22%3B%7D; advanced-frontend-fmz=tkg1mvdevem4bjme5kqtr5uqg2; _csrf-frontend=47bf2bb7806c31ad01969b32126f9d22af283e8fa8d9d341741e6b3b18a1efb5a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22zNp9QwIxMks_UotPrUD9o5WeVZdsiCsS%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:17:"HTTP_CONTENT_TYPE";s:33:"application/x-www-form-urlencoded";s:19:"HTTP_CONTENT_LENGTH";s:4:"1115";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:18:"HTTP_CACHE_CONTROL";s:9:"max-age=0";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:21:"/backoffice/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.223098;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:2:{s:1:"r";s:25:"role-functionality/routes";s:7:"role_id";s:1:"4";}s:4:"POST";a:3:{s:13:"_csrf-backend";s:88:"NRbz7LqfI-Wkjf48YayGKXcf7CbBaQ1Zrb5awyjOA95bZ4mh48sOgN78hnQp9up7KCikdKY2Z2D0jwiUeKh7pg==";s:17:"RoleFunctionality";a:2:{s:7:"role_id";s:1:"4";s:5:"route";a:16:{i:0;s:19:"/gedetineerde/index";i:1;s:11:"/site/index";i:2;s:26:"/document-signature/create";i:3;s:34:"/document-signature/save-signature";i:4;s:23:"/document/view-document";i:5;s:15:"/document/index";i:6;s:16:"/document/update";i:7;s:16:"/document/create";i:8;s:19:"/document/getvragen";i:9;s:22:"/document-upload/index";i:10;s:18:"/document/dossiers";i:11;s:18:"/document/document";i:12;s:19:"/document/signature";i:13;s:14:"/document/view";i:14;s:22:"/document/viewdocument";i:15;s:18:"/document/viewfile";}}s:12:"custom_route";s:12:"gedetineerde";}s:6:"COOKIE";a:9:{s:18:"_identity-frontend";s:158:"ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a:2:{i:0;s:18:"_identity-frontend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:16:"sidebar-collapse";s:5:"false";s:17:"advanced-frontend";s:26:"3fp8o6megq4gr3o67bnqks3c7n";s:20:"advanced-backend-fmz";s:26:"b7df4be0nkp5g9vhkif794pfkf";s:21:"advanced-backend-test";s:26:"o4hu6edu0utm57lp4o2h9jjjj9";s:17:"_identity-backend";s:157:"946df6687b5fb74a9b0eaa13756e2019f90ea1fbcefd9b8ca4919bcb5352521aa:2:{i:0;s:17:"_identity-backend";i:1;s:46:"[1,"RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo",2592000]";}";s:13:"_csrf-backend";s:139:"0990b5f7dba8d91868f791f8029ebc7434255b71b1a85dc1ee8d4eff22c10b19a:2:{i:0;s:13:"_csrf-backend";i:1;s:32:"nqzMYT-ezqxHHZlR_7HRg_j9Y1RWPfxx";}";s:21:"advanced-frontend-fmz";s:26:"tkg1mvdevem4bjme5kqtr5uqg2";s:14:"_csrf-frontend";s:140:"47bf2bb7806c31ad01969b32126f9d22af283e8fa8d9d341741e6b3b18a1efb5a:2:{i:0;s:14:"_csrf-frontend";i:1;s:32:"zNp9QwIxMks_UotPrUD9o5WeVZdsiCsS";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:4:{s:7:"__flash";a:1:{s:7:"success";i:-1;}s:4:"__id";i:1;s:9:"__authKey";s:32:"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW";s:7:"success";s:28:"Routes updated successfully.";}}";s:4:"user";s:1549:"a:5:{s:2:"id";i:1;s:8:"identity";a:12:{s:2:"id";s:1:"1";s:8:"username";s:11:"'SuperUser'";s:8:"auth_key";s:34:"'R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW'";s:13:"password_hash";s:62:"'$2y$13$uUSLwNI3so2bzilzzZdKJ.C1qmfyTdLRRT1XVP8jYO1c38iE6dfxS'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:17:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"1741788198";s:10:"updated_at";s:10:"1749661338";s:18:"verification_token";s:45:"'oZxacBEp5N7LZAqXc3s1haihOCLK8dLU_1741788198'";s:7:"role_id";s:1:"1";s:12:"access_token";s:66:"'CjYetJZp6PI5vAvKuqm6TDSC_2kYfe_LtOkrjMVtEo4IseTg3J-r-Gp3gMgH-pr7'";}s:10:"attributes";a:12:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:8:"Password";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:4:"Role";}i:11;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"68a35dd84babf";s:3:"url";s:82:"http://localhost:8005/backoffice/index.php?r=role-functionality%2Froutes&role_id=4";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:3:"::1";s:4:"time";d:**********.223098;s:10:"statusCode";i:302;s:8:"sqlCount";i:78;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9161928;s:14:"processingTime";d:0.27266502380371094;}s:10:"exceptions";a:0:{}}