<?php

use yii\helpers\Html;
use kartik\form\ActiveForm;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use common\models\DocumentType;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $model common\models\Rapport */
/* @var $form yii\widgets\ActiveForm */


// Prepare data for dropdown
$documents = ArrayHelper::map(DocumentType::find()->all(), 'id', 'type');
?>

<div class="rapport-form">
    <?php $form = ActiveForm::begin([
        'id' => 'rapport-form',
        'options' => [
            'data-pjax' => true, // Add this to identify it as a Pjax form
        ],
    ]); ?>

    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'documenttype_id')->widget(Select2::classname(), [
                'data' => $documents,
                'options' => [
                    'placeholder' => 'Select a document...',
                    'id' => 'document-select'
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'dropdownParent' => '#ajaxCrudModal'
                ]
            ]); ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'filename')->textInput(['maxlength' => true]) ?>
        </div>
    </div>


    <div id="questions-container" style="display:none;">
        <div id="questions-list" class="mb-3">
            <!-- Questions will be loaded here -->
        </div>
    </div>

    <?php if (!Yii::$app->request->isAjax) { ?>
        <div class="form-group">
            <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
        </div>
    <?php } ?>

    <?php ActiveForm::end(); ?>

</div>

<?php
$getVragenUrl = Url::to(['document/get-vragen']);

$script = <<<JS
// When Select2 value changes
$('#document-select').on('change', function() {
    const docId = $(this).val();
    const questionsContainer = $('#questions-container');
    const questionsList = $('#questions-list');
    
    if (!docId) {
        questionsContainer.hide();
        questionsList.empty();
        return;
    }

    // Fetch questions using AJAX
    $.get('$getVragenUrl', { docId: docId })
        .done(function(response) {
            if (response.success) {
                questionsList.empty();
                
                // Create questions list with input fields
                const questions = response.vragen;
                questions.forEach(function(item) {
                    const vraag = item.vraag;
                    questionsList.append(`
                        <div class="form-group mb-3">
                            <label class="control-label" for="answer-\${vraag.id}">
                                \${vraag.vraag_nummer}. \${vraag.vraag}
                            </label>
                            <input type="text" 
                                   id="answer-\${vraag.id}" 
                                   class="form-control" 
                                   name="Answers[\${vraag.id}]" 
                                   data-question-id="\${vraag.id}">
                        </div>
                    `);
                });
                
                questionsContainer.show();
            } else {
                questionsContainer.hide();
                questionsList.html('<div class="alert alert-warning">No questions found</div>');
            }
        })
        .fail(function() {
            questionsContainer.hide();
            questionsList.html('<div class="alert alert-danger">Error loading questions</div>');
        });
});

// Prevent duplicate form submissions
let isSubmitting = false;
$('#rapport-form').on('beforeSubmit', function(e) {
    if (isSubmitting) {
        return false;
    }
    
    isSubmitting = true;
    return true;
});
JS;

$this->registerJs($script);
?>