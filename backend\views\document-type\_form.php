<?php

use dosamigos\ckeditor\CKEditor;
use kartik\form\ActiveForm;
use yii\helpers\Html;

/* @var $this yii\web\View */
/* @var $model common\models\DocumentNaam */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="document-naam-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'type')->textInput(['maxlength' => true]) ?>
    <?= $form->field($model, 'layout_template')->widget(CKEditor::class, [
        'options' => ['rows' => 40, 'style' => 'border-radius:8px'],
        'preset' => 'custom',
        'clientOptions' => [
            'extraPlugins' => 'justify',
            'entities' => false, // ← prevent encoding of &, <, >, etc.
            'basicEntities' => false,
            'htmlEncodeOutput' => false,
            'allowedContent' => true, // allow full HTML and Twig
            'extraAllowedContent' => '*[*]{*}(*);', // allow any tags/attrs
        ],
    ]); ?>

    <?php if (!Yii::$app->request->isAjax) { ?>
        <div class="form-group">
            <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
        </div>
    <?php } ?>

    <?php ActiveForm::end(); ?>

</div>