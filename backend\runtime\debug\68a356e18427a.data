a:14:{s:6:"config";s:15659:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:3:"FMZ";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:70:{s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\Reclassering\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\Reclassering\vendor/yiisoft/yii2-symfonymailer/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte-widgets/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap4/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte3/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-mpdf/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-dialog/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\Reclassering\vendor/kartik-v/yii2-popover-x/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-builder/src";}}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-editable/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-grid/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-helpers/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-context-menu/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-datecontrol/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-httpclient/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-detail-view/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-date-range/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-dynagrid/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\Reclassering\vendor/yiisoft/yii2-jui";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-label-inplace/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-icons/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-money/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-markdown/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\Reclassering\vendor/kartik-v/yii2-slider";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-tabs-x/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-typeahead/src";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-social/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-touchspin/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-switchinput";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-depdrop/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-colorinput/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:66:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:12:"@kartik/date";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datepicker/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-alert/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-affix";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-widgets/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\Reclassering\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\Reclassering\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-select2/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-activeform/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow/src";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-export/src";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\Reclassering\vendor/cornernote/yii2-workflow-manager/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\Reclassering\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\Reclassering\vendor/yiisoft/yii2-twig/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-debug/src";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\Reclassering\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\Reclassering\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-fileinput/src";}}s:24:"rmrevin/yii2-fontawesome";a:3:{s:4:"name";s:24:"rmrevin/yii2-fontawesome";s:7:"version";s:8:"2.10.3.0";s:5:"alias";a:1:{s:24:"@rmrevin/yii/fontawesome";s:51:"C:\Web\Reclassering\vendor/rmrevin/yii2-fontawesome";}}s:24:"edofre/yii2-fullcalendar";a:3:{s:4:"name";s:24:"edofre/yii2-fullcalendar";s:7:"version";s:8:"1.0.11.0";s:5:"alias";a:1:{s:20:"@edofre/fullcalendar";s:55:"C:\Web\Reclassering\vendor/edofre/yii2-fullcalendar/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/time";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-timepicker/src";}}}}";s:3:"log";s:30019:"a:1:{s:8:"messages";a:60:{i:0;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.480914;i:4;a:0:{}i:5;i:2906624;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.481507;i:4;a:0:{}i:5;i:3011848;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.481513;i:4;a:0:{}i:5;i:3012144;}i:3;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.481955;i:4;a:0:{}i:5;i:3042336;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.482426;i:4;a:0:{}i:5;i:3069808;}i:5;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.484834;i:4;a:0:{}i:5;i:3224424;}i:6;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.484844;i:4;a:0:{}i:5;i:3225064;}i:7;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.489662;i:4;a:0:{}i:5;i:4222720;}i:8;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.495315;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5546816;}i:9;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.495339;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5549096;}i:14;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.513426;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5606912;}i:17;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.514544;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5627632;}i:20;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.52689;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6078400;}i:23;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.539701;i:4;a:0:{}i:5;i:6662264;}i:24;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.542758;i:4;a:0:{}i:5;i:6776904;}i:25;a:6:{i:0;s:21:"Loading module: audit";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.542794;i:4;a:0:{}i:5;i:6777544;}i:26;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.544806;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6913000;}i:29;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.547511;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6924632;}i:32;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.548922;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6924416;}i:35;a:6:{i:0;s:40:"Bootstrap with bedezign\yii2\audit\Audit";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.562606;i:4;a:0:{}i:5;i:7104848;}i:37;a:6:{i:0;s:44:"Route requested: 'role-functionality/routes'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.565539;i:4;a:0:{}i:5;i:7230264;}i:38;a:6:{i:0;s:39:"Route to run: role-functionality/routes";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.568458;i:4;a:0:{}i:5;i:7420392;}i:39;a:6:{i:0;s:172:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/routes', '1', '::1', 0, 'POST', '2025-08-18 17:37:53')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.577342;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7680424;}i:42;a:6:{i:0;s:79:"Running action: backend\controllers\RoleFunctionalityController::actionRoutes()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.582384;i:4;a:0:{}i:5;i:7689816;}i:43;a:6:{i:0;s:52:"DELETE FROM `role_functionality` WHERE `role_id`='4'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.58698;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:289;s:8:"function";s:9:"deleteAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}i:5;i:7785912;}i:46;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.590824;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7796696;}i:49;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.593421;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7808432;}i:52;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.594038;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7808328;}i:55;a:6:{i:0;s:192:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/site/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.597797;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8013584;}i:58;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.601374;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8020296;}i:61;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.604437;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8043392;}i:64;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.606683;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8050104;}i:67;a:6:{i:0;s:215:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/save-signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.607893;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8075072;}i:70;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.610018;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8081784;}i:73;a:6:{i:0;s:204:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view-document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.610896;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8104832;}i:76;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.613799;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8111544;}i:79;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.615059;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8134560;}i:82;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.618485;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8141272;}i:85;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/update', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.619996;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8165568;}i:88;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.622898;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8172280;}i:91;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.623804;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8195296;}i:94;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.625458;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8202008;}i:97;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/getvragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.626102;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8225056;}i:100;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.6275;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8231768;}i:103;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-upload/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.629666;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8254816;}i:106;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.631486;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8261528;}i:109;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/dossiers', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.632913;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8284544;}i:112;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.63475;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8291256;}i:115;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.6371;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8314272;}i:118;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.639882;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8320984;}i:121;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.640798;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8344032;}i:124;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.64238;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8356376;}i:127;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.642991;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8379392;}i:130;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.644692;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8391736;}i:133;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewdocument', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.645238;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8414784;}i:136;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.646954;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8421496;}i:139;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewfile', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.647725;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8444512;}i:142;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.649445;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8451224;}i:145;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/routes'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.651822;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8499128;}i:148;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.17754602432251, `memory_max`=8553552 WHERE `id`=2514";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.652402;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:8501944;}}}";s:9:"profiling";s:55278:"a:3:{s:6:"memory";i:8954840;s:4:"time";d:0.1813819408416748;s:8:"messages";a:90:{i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.495345;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5549904;}i:11;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.511267;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5593208;}i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.511299;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5592992;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.513368;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5605624;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.51344;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5607824;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.514007;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5610400;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.514562;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5628672;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.517947;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5631200;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.526961;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6078784;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.527833;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6081152;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.544871;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6913912;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.547458;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6923344;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.547529;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6925544;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.548704;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6927456;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.548944;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6926096;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.550578;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6927952;}i:40;a:6:{i:0;s:172:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/routes', '1', '::1', 0, 'POST', '2025-08-18 17:37:53')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.577445;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7681784;}i:41;a:6:{i:0;s:172:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/routes', '1', '::1', 0, 'POST', '2025-08-18 17:37:53')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.58151;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7683576;}i:44;a:6:{i:0;s:52:"DELETE FROM `role_functionality` WHERE `role_id`='4'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.587024;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:289;s:8:"function";s:9:"deleteAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}i:5;i:7787040;}i:45;a:6:{i:0;s:52:"DELETE FROM `role_functionality` WHERE `role_id`='4'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.590714;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:289;s:8:"function";s:9:"deleteAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}i:5;i:7787952;}i:47;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.59084;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7797608;}i:48;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.59338;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7807136;}i:50;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.593434;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7809344;}i:51;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.59393;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7811648;}i:53;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.594049;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7809368;}i:54;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.595639;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7812456;}i:56;a:6:{i:0;s:192:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/site/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.59785;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8014296;}i:57;a:6:{i:0;s:192:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/site/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.601071;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8015368;}i:59;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.601403;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8022176;}i:60;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.603494;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8026232;}i:62;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.60445;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8044104;}i:63;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.606238;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8045176;}i:65;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.606736;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8053264;}i:66;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.607363;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8057320;}i:68;a:6:{i:0;s:215:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/save-signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.607912;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8075784;}i:69;a:6:{i:0;s:215:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/save-signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.609892;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8076920;}i:71;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.61003;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8083664;}i:72;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.610484;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8087720;}i:74;a:6:{i:0;s:204:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view-document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.610905;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8105544;}i:75;a:6:{i:0;s:204:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view-document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.613498;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8106616;}i:77;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.613849;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8113424;}i:78;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.614488;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8117480;}i:80;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.615083;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8135272;}i:81;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.618323;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8136344;}i:83;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.618499;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8143152;}i:84;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.619279;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8147208;}i:86;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/update', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.620008;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8166280;}i:87;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/update', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.622485;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8167352;}i:89;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.622952;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8174160;}i:90;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.62342;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8178216;}i:92;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.623813;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8196008;}i:93;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.625375;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8197080;}i:95;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.625467;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8203888;}i:96;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.625779;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8207944;}i:98;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/getvragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.626111;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8225768;}i:99;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/getvragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.627414;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8226840;}i:101;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.627508;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8233648;}i:102;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.627856;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8237704;}i:104;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-upload/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.629713;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8255528;}i:105;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-upload/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.631251;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8256600;}i:107;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.631522;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8263408;}i:108;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.632067;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8267464;}i:110;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/dossiers', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.632923;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8285256;}i:111;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/dossiers', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.634656;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8286328;}i:113;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.63476;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8293136;}i:114;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.635144;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8297192;}i:116;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.637153;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8314984;}i:117;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.639647;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8316056;}i:119;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.639901;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8322864;}i:120;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.64045;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8326920;}i:122;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.640807;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8344744;}i:123;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.642315;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8345816;}i:125;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.642388;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8358256;}i:126;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.642679;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8362312;}i:128;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.643;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8380104;}i:129;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.644634;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8386808;}i:131;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.6447;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8393616;}i:132;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.64497;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8397672;}i:134;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewdocument', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.645246;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8415496;}i:135;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewdocument', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.646668;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8416568;}i:137;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.646998;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8423376;}i:138;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.647382;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8427432;}i:140;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewfile', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.647734;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8445224;}i:141;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewfile', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.649253;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8446296;}i:143;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.649459;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8453104;}i:144;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.649944;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8457160;}i:146;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/routes'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.651868;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8500256;}i:147;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/routes'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.652308;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8501408;}i:149;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.17754602432251, `memory_max`=8553552 WHERE `id`=2514";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.652415;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:8503288;}i:150;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.17754602432251, `memory_max`=8553552 WHERE `id`=2514";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.653939;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:8504688;}}}";s:2:"db";s:54509:"a:1:{s:8:"messages";a:88:{i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.511299;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5592992;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.513368;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5605624;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.51344;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5607824;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.514007;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5610400;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.514562;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5628672;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.517947;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5631200;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.526961;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6078784;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.527833;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6081152;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.544871;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6913912;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.547458;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6923344;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.547529;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6925544;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.548704;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6927456;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.548944;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6926096;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.550578;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:6927952;}i:40;a:6:{i:0;s:172:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/routes', '1', '::1', 0, 'POST', '2025-08-18 17:37:53')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.577445;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7681784;}i:41;a:6:{i:0;s:172:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/routes', '1', '::1', 0, 'POST', '2025-08-18 17:37:53')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.58151;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7683576;}i:44;a:6:{i:0;s:52:"DELETE FROM `role_functionality` WHERE `role_id`='4'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.587024;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:289;s:8:"function";s:9:"deleteAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}i:5;i:7787040;}i:45;a:6:{i:0;s:52:"DELETE FROM `role_functionality` WHERE `role_id`='4'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.590714;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:289;s:8:"function";s:9:"deleteAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}i:5;i:7787952;}i:47;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.59084;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7797608;}i:48;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.59338;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7807136;}i:50;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.593434;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7809344;}i:51;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.59393;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7811648;}i:53;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.594049;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7809368;}i:54;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.595639;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7812456;}i:56;a:6:{i:0;s:192:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/site/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.59785;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8014296;}i:57;a:6:{i:0;s:192:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/site/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.601071;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8015368;}i:59;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.601403;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8022176;}i:60;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.603494;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8026232;}i:62;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.60445;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8044104;}i:63;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.606238;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8045176;}i:65;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.606736;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8053264;}i:66;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.607363;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8057320;}i:68;a:6:{i:0;s:215:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/save-signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.607912;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8075784;}i:69;a:6:{i:0;s:215:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/save-signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.609892;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8076920;}i:71;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.61003;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8083664;}i:72;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.610484;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8087720;}i:74;a:6:{i:0;s:204:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view-document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.610905;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8105544;}i:75;a:6:{i:0;s:204:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view-document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.613498;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8106616;}i:77;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.613849;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8113424;}i:78;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.614488;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8117480;}i:80;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.615083;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8135272;}i:81;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.618323;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8136344;}i:83;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.618499;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8143152;}i:84;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.619279;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8147208;}i:86;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/update', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.620008;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8166280;}i:87;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/update', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.622485;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8167352;}i:89;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.622952;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8174160;}i:90;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.62342;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8178216;}i:92;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.623813;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8196008;}i:93;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.625375;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8197080;}i:95;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.625467;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8203888;}i:96;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.625779;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8207944;}i:98;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/getvragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.626111;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8225768;}i:99;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/getvragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.627414;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8226840;}i:101;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.627508;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8233648;}i:102;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.627856;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8237704;}i:104;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-upload/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.629713;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8255528;}i:105;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-upload/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.631251;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8256600;}i:107;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.631522;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8263408;}i:108;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.632067;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8267464;}i:110;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/dossiers', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.632923;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8285256;}i:111;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/dossiers', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.634656;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8286328;}i:113;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.63476;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8293136;}i:114;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.635144;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8297192;}i:116;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.637153;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8314984;}i:117;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.639647;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8316056;}i:119;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.639901;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8322864;}i:120;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.64045;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8326920;}i:122;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.640807;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8344744;}i:123;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.642315;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8345816;}i:125;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.642388;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8358256;}i:126;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.642679;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8362312;}i:128;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.643;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8380104;}i:129;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.644634;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8386808;}i:131;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.6447;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8393616;}i:132;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.64497;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8397672;}i:134;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewdocument', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.645246;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8415496;}i:135;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewdocument', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.646668;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8416568;}i:137;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.646998;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8423376;}i:138;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.647382;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8427432;}i:140;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewfile', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.647734;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8445224;}i:141;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewfile', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.649253;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8446296;}i:143;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.649459;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8453104;}i:144;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.649944;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:183;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:169;s:8:"function";s:24:"invalidateRoleUsersCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8457160;}i:146;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/routes'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.651868;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8500256;}i:147;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/routes'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.652308;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8501408;}i:149;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.17754602432251, `memory_max`=8553552 WHERE `id`=2514";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.652415;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:8503288;}i:150;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.17754602432251, `memory_max`=8553552 WHERE `id`=2514";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.653939;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:8504688;}}}";s:5:"event";s:30897:"a:172:{i:0;a:5:{s:4:"time";d:**********.493684;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:**********.511253;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:**********.528261;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:**********.528302;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:**********.534527;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:5;a:5:{s:4:"time";d:**********.565504;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:**********.571745;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:7;a:5:{s:4:"time";d:**********.571815;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:8;a:5:{s:4:"time";d:**********.575961;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:9;a:5:{s:4:"time";d:**********.581932;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:10;a:5:{s:4:"time";d:**********.581954;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:11;a:5:{s:4:"time";d:**********.582285;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:47:"backend\controllers\RoleFunctionalityController";}i:12;a:5:{s:4:"time";d:**********.584827;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:13;a:5:{s:4:"time";d:**********.590783;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:14;a:5:{s:4:"time";d:**********.595675;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:15;a:5:{s:4:"time";d:**********.597131;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:16;a:5:{s:4:"time";d:**********.597171;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:17;a:5:{s:4:"time";d:**********.601184;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:18;a:5:{s:4:"time";d:**********.601225;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:19;a:5:{s:4:"time";d:**********.603545;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:20;a:5:{s:4:"time";d:**********.603584;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:21;a:5:{s:4:"time";d:**********.603597;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:22;a:5:{s:4:"time";d:**********.6036;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:23;a:5:{s:4:"time";d:**********.604191;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:24;a:5:{s:4:"time";d:**********.60421;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:25;a:5:{s:4:"time";d:**********.604336;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:26;a:5:{s:4:"time";d:**********.604366;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:27;a:5:{s:4:"time";d:**********.606312;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:28;a:5:{s:4:"time";d:**********.606462;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:29;a:5:{s:4:"time";d:**********.607395;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:30;a:5:{s:4:"time";d:**********.607423;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:31;a:5:{s:4:"time";d:**********.607433;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:32;a:5:{s:4:"time";d:**********.607436;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:33;a:5:{s:4:"time";d:**********.607608;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:34;a:5:{s:4:"time";d:**********.607622;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:35;a:5:{s:4:"time";d:**********.607797;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:36;a:5:{s:4:"time";d:**********.607828;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:37;a:5:{s:4:"time";d:**********.609944;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:38;a:5:{s:4:"time";d:**********.609961;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:39;a:5:{s:4:"time";d:**********.610508;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:40;a:5:{s:4:"time";d:**********.610534;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:41;a:5:{s:4:"time";d:**********.610543;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:42;a:5:{s:4:"time";d:**********.610546;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:43;a:5:{s:4:"time";d:**********.610708;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:44;a:5:{s:4:"time";d:**********.610719;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:45;a:5:{s:4:"time";d:**********.610826;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:46;a:5:{s:4:"time";d:**********.610849;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:47;a:5:{s:4:"time";d:**********.613554;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:48;a:5:{s:4:"time";d:**********.613607;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:49;a:5:{s:4:"time";d:**********.61451;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:50;a:5:{s:4:"time";d:**********.614529;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:51;a:5:{s:4:"time";d:**********.614542;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:52;a:5:{s:4:"time";d:**********.614545;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:53;a:5:{s:4:"time";d:**********.614687;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:54;a:5:{s:4:"time";d:**********.6147;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:55;a:5:{s:4:"time";d:**********.614872;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:56;a:5:{s:4:"time";d:**********.614902;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:57;a:5:{s:4:"time";d:**********.618393;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:58;a:5:{s:4:"time";d:**********.618416;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:59;a:5:{s:4:"time";d:**********.619555;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:60;a:5:{s:4:"time";d:**********.619623;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:61;a:5:{s:4:"time";d:**********.619635;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:62;a:5:{s:4:"time";d:**********.619638;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:63;a:5:{s:4:"time";d:**********.61979;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:64;a:5:{s:4:"time";d:**********.619805;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:65;a:5:{s:4:"time";d:**********.619917;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:66;a:5:{s:4:"time";d:**********.619941;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:67;a:5:{s:4:"time";d:**********.622615;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:68;a:5:{s:4:"time";d:**********.622681;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:69;a:5:{s:4:"time";d:**********.623455;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:70;a:5:{s:4:"time";d:**********.623483;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:71;a:5:{s:4:"time";d:**********.623493;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:72;a:5:{s:4:"time";d:**********.623496;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:73;a:5:{s:4:"time";d:**********.623627;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:74;a:5:{s:4:"time";d:**********.623638;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:75;a:5:{s:4:"time";d:**********.623739;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:76;a:5:{s:4:"time";d:**********.62376;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:77;a:5:{s:4:"time";d:**********.625404;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:78;a:5:{s:4:"time";d:**********.625417;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:79;a:5:{s:4:"time";d:**********.625797;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:80;a:5:{s:4:"time";d:**********.625818;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:81;a:5:{s:4:"time";d:**********.625828;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:82;a:5:{s:4:"time";d:**********.62583;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:83;a:5:{s:4:"time";d:**********.625945;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:84;a:5:{s:4:"time";d:**********.625954;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:85;a:5:{s:4:"time";d:**********.626045;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:86;a:5:{s:4:"time";d:**********.626064;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:87;a:5:{s:4:"time";d:**********.627443;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:88;a:5:{s:4:"time";d:**********.627455;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:89;a:5:{s:4:"time";d:**********.628011;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:90;a:5:{s:4:"time";d:**********.628135;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:91;a:5:{s:4:"time";d:**********.628194;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:92;a:5:{s:4:"time";d:**********.628211;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:93;a:5:{s:4:"time";d:**********.628782;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:94;a:5:{s:4:"time";d:**********.628831;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:95;a:5:{s:4:"time";d:**********.629311;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:96;a:5:{s:4:"time";d:**********.62941;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:97;a:5:{s:4:"time";d:**********.631287;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:98;a:5:{s:4:"time";d:**********.6313;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:99;a:5:{s:4:"time";d:**********.632121;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:100;a:5:{s:4:"time";d:**********.63219;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:101;a:5:{s:4:"time";d:**********.632231;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:102;a:5:{s:4:"time";d:**********.632234;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:103;a:5:{s:4:"time";d:**********.632488;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:104;a:5:{s:4:"time";d:**********.632537;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:105;a:5:{s:4:"time";d:**********.63284;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:106;a:5:{s:4:"time";d:**********.632863;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:107;a:5:{s:4:"time";d:**********.634687;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:108;a:5:{s:4:"time";d:**********.634701;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:109;a:5:{s:4:"time";d:**********.635231;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:110;a:5:{s:4:"time";d:**********.635354;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:111;a:5:{s:4:"time";d:**********.635415;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:112;a:5:{s:4:"time";d:**********.635432;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:113;a:5:{s:4:"time";d:**********.636061;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:114;a:5:{s:4:"time";d:**********.636119;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:115;a:5:{s:4:"time";d:**********.636716;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:116;a:5:{s:4:"time";d:**********.63686;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:117;a:5:{s:4:"time";d:**********.639732;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:118;a:5:{s:4:"time";d:**********.639783;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:119;a:5:{s:4:"time";d:**********.640477;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:120;a:5:{s:4:"time";d:**********.6405;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:121;a:5:{s:4:"time";d:**********.64051;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:122;a:5:{s:4:"time";d:**********.640512;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:123;a:5:{s:4:"time";d:**********.64063;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:124;a:5:{s:4:"time";d:**********.64064;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:125;a:5:{s:4:"time";d:**********.640736;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:126;a:5:{s:4:"time";d:**********.640756;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:127;a:5:{s:4:"time";d:**********.642336;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:128;a:5:{s:4:"time";d:**********.642346;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:129;a:5:{s:4:"time";d:**********.642695;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:130;a:5:{s:4:"time";d:**********.642714;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:131;a:5:{s:4:"time";d:**********.642723;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:132;a:5:{s:4:"time";d:**********.642725;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:133;a:5:{s:4:"time";d:**********.642836;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:134;a:5:{s:4:"time";d:**********.642845;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:135;a:5:{s:4:"time";d:**********.642935;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:136;a:5:{s:4:"time";d:**********.642954;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:137;a:5:{s:4:"time";d:**********.644652;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:138;a:5:{s:4:"time";d:**********.644661;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:139;a:5:{s:4:"time";d:**********.644984;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:140;a:5:{s:4:"time";d:**********.645001;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:141;a:5:{s:4:"time";d:**********.64501;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:142;a:5:{s:4:"time";d:**********.645012;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:143;a:5:{s:4:"time";d:**********.645099;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:144;a:5:{s:4:"time";d:**********.645107;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:145;a:5:{s:4:"time";d:**********.645186;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:146;a:5:{s:4:"time";d:**********.645203;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:147;a:5:{s:4:"time";d:**********.646732;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:148;a:5:{s:4:"time";d:**********.646783;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:149;a:5:{s:4:"time";d:**********.647409;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:150;a:5:{s:4:"time";d:**********.647433;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:151;a:5:{s:4:"time";d:**********.647443;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:152;a:5:{s:4:"time";d:**********.647445;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:153;a:5:{s:4:"time";d:**********.647566;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:154;a:5:{s:4:"time";d:**********.647576;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:155;a:5:{s:4:"time";d:**********.647664;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:156;a:5:{s:4:"time";d:**********.647683;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:157;a:5:{s:4:"time";d:**********.649356;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:158;a:5:{s:4:"time";d:**********.649381;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:159;a:5:{s:4:"time";d:**********.650084;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:160;a:5:{s:4:"time";d:**********.650171;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:161;a:5:{s:4:"time";d:**********.65019;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:162;a:5:{s:4:"time";d:**********.650193;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:163;a:5:{s:4:"time";d:**********.651078;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:47:"backend\controllers\RoleFunctionalityController";}i:164;a:5:{s:4:"time";d:**********.651552;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:165;a:5:{s:4:"time";d:**********.652319;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:166;a:5:{s:4:"time";d:**********.652352;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:167;a:5:{s:4:"time";d:**********.653957;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:168;a:5:{s:4:"time";d:**********.653962;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:169;a:5:{s:4:"time";d:**********.653967;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:170;a:5:{s:4:"time";d:**********.654338;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:171;a:5:{s:4:"time";d:**********.65436;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:**********.474786;s:3:"end";d:**********.657565;s:6:"memory";i:8954840;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:334:"a:3:{s:8:"messages";a:1:{i:36;a:6:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.56552;i:4;a:0:{}i:5;i:7230344;}}s:5:"route";s:25:"role-functionality/routes";s:6:"action";s:63:"backend\controllers\RoleFunctionalityController::actionRoutes()";}";s:7:"request";s:14082:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:302;s:14:"requestHeaders";a:20:{s:12:"content-type";s:33:"application/x-www-form-urlencoded";s:14:"content-length";s:4:"1044";s:14:"sec-fetch-dest";s:8:"document";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-site";s:11:"same-origin";s:25:"upgrade-insecure-requests";s:1:"1";s:6:"origin";s:21:"http://localhost:8005";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:65:""Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:7:"referer";s:82:"http://localhost:8005/backoffice/index.php?r=role-functionality%2Froutes&role_id=4";s:4:"host";s:14:"localhost:8005";s:6:"cookie";s:1053:"_identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; advanced-frontend=3fp8o6megq4gr3o67bnqks3c7n; advanced-backend-fmz=b7df4be0nkp5g9vhkif794pfkf; advanced-backend-test=o4hu6edu0utm57lp4o2h9jjjj9; _identity-backend=946df6687b5fb74a9b0eaa13756e2019f90ea1fbcefd9b8ca4919bcb5352521aa%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; _csrf-backend=0990b5f7dba8d91868f791f8029ebc7434255b71b1a85dc1ee8d4eff22c10b19a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22nqzMYT-ezqxHHZlR_7HRg_j9Y1RWPfxx%22%3B%7D; advanced-frontend-fmz=tkg1mvdevem4bjme5kqtr5uqg2; _csrf-frontend=f1520d10f90a11651376bef1453682b0b98ce980053a428d4c8b8eb96fa1ef01a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22yzNq8LytO5crIYQuyaMfv0GNFlyggF73%22%3B%7D";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:10:"connection";s:10:"keep-alive";s:13:"cache-control";s:9:"max-age=0";}s:15:"responseHeaders";a:9:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:8:"Location";s:82:"http://localhost:8005/backoffice/index.php?r=role-functionality%2Froutes&role_id=4";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68a356e18427a";s:16:"X-Debug-Duration";s:3:"181";s:12:"X-Debug-Link";s:64:"/backoffice/index.php?r=debug%2Fdefault%2Fview&tag=68a356e18427a";}s:5:"route";s:25:"role-functionality/routes";s:6:"action";s:63:"backend\controllers\RoleFunctionalityController::actionRoutes()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:33:"application/x-www-form-urlencoded";s:3:"Raw";s:1044:"_csrf-backend=PnmwB6sk1i9AHH1f63QB1oCO6j73CkNoWLeZrsqfS_lQCMpK8nD7SjptBRejLm2E37mibJBVKVEBhsv5mvkzgQ%3D%3D&RoleFunctionality%5Brole_id%5D=4&custom_route=&RoleFunctionality%5Broute%5D%5B%5D=%2Fsite%2Findex&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument-signature%2Fcreate&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument-signature%2Fsave-signature&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fview-document&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Findex&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fupdate&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fcreate&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fgetvragen&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument-upload%2Findex&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fdossiers&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fdocument&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fsignature&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fview&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fviewdocument&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fviewfile";s:7:"Decoded";a:3:{s:13:"_csrf-backend";s:88:"PnmwB6sk1i9AHH1f63QB1oCO6j73CkNoWLeZrsqfS_lQCMpK8nD7SjptBRejLm2E37mibJBVKVEBhsv5mvkzgQ==";s:17:"RoleFunctionality";a:2:{s:7:"role_id";s:1:"4";s:5:"route";a:15:{i:0;s:11:"/site/index";i:1;s:26:"/document-signature/create";i:2;s:34:"/document-signature/save-signature";i:3;s:23:"/document/view-document";i:4;s:15:"/document/index";i:5;s:16:"/document/update";i:6;s:16:"/document/create";i:7;s:19:"/document/getvragen";i:8;s:22:"/document-upload/index";i:9;s:18:"/document/dossiers";i:10;s:18:"/document/document";i:11;s:19:"/document/signature";i:12;s:14:"/document/view";i:13;s:22:"/document/viewdocument";i:14;s:18:"/document/viewfile";}}s:12:"custom_route";s:0:"";}}s:6:"SERVER";a:108:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-ed16ee87-8374-48f4-8d9e-abbd1f7991db";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Reclassering\Reclassering.config";s:11:"APP_POOL_ID";s:12:"Reclassering";s:17:"ChocolateyInstall";s:25:"C:\ProgramData\chocolatey";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:553:"C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:21:"/backoffice/index.php";s:3:"URL";s:21:"/backoffice/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"8005";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:21:"/backoffice/index.php";s:15:"SCRIPT_FILENAME";s:41:"C:\Web\Reclassering\backend\web\index.php";s:11:"REQUEST_URI";s:61:"/backoffice/index.php?r=role-functionality%2Froutes&role_id=4";s:14:"REQUEST_METHOD";s:4:"POST";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"59392";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:39:"r=role-functionality%2Froutes&role_id=4";s:15:"PATH_TRANSLATED";s:41:"C:\Web\Reclassering\backend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/2";s:13:"INSTANCE_NAME";s:12:"RECLASSERING";s:11:"INSTANCE_ID";s:1:"2";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\Reclassering\frontend\web";s:12:"CONTENT_TYPE";s:33:"application/x-www-form-urlencoded";s:14:"CONTENT_LENGTH";s:4:"1044";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:32:"C:\Web\Reclassering\backend\web\";s:12:"APPL_MD_PATH";s:27:"/LM/W3SVC/2/ROOT/backoffice";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:11:"HTTP_ORIGIN";s:21:"http://localhost:8005";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:65:""Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:12:"HTTP_REFERER";s:82:"http://localhost:8005/backoffice/index.php?r=role-functionality%2Froutes&role_id=4";s:9:"HTTP_HOST";s:14:"localhost:8005";s:11:"HTTP_COOKIE";s:1053:"_identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; advanced-frontend=3fp8o6megq4gr3o67bnqks3c7n; advanced-backend-fmz=b7df4be0nkp5g9vhkif794pfkf; advanced-backend-test=o4hu6edu0utm57lp4o2h9jjjj9; _identity-backend=946df6687b5fb74a9b0eaa13756e2019f90ea1fbcefd9b8ca4919bcb5352521aa%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; _csrf-backend=0990b5f7dba8d91868f791f8029ebc7434255b71b1a85dc1ee8d4eff22c10b19a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22nqzMYT-ezqxHHZlR_7HRg_j9Y1RWPfxx%22%3B%7D; advanced-frontend-fmz=tkg1mvdevem4bjme5kqtr5uqg2; _csrf-frontend=f1520d10f90a11651376bef1453682b0b98ce980053a428d4c8b8eb96fa1ef01a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22yzNq8LytO5crIYQuyaMfv0GNFlyggF73%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:17:"HTTP_CONTENT_TYPE";s:33:"application/x-www-form-urlencoded";s:19:"HTTP_CONTENT_LENGTH";s:4:"1044";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:18:"HTTP_CACHE_CONTROL";s:9:"max-age=0";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:21:"/backoffice/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.466413;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:2:{s:1:"r";s:25:"role-functionality/routes";s:7:"role_id";s:1:"4";}s:4:"POST";a:3:{s:13:"_csrf-backend";s:88:"PnmwB6sk1i9AHH1f63QB1oCO6j73CkNoWLeZrsqfS_lQCMpK8nD7SjptBRejLm2E37mibJBVKVEBhsv5mvkzgQ==";s:17:"RoleFunctionality";a:2:{s:7:"role_id";s:1:"4";s:5:"route";a:15:{i:0;s:11:"/site/index";i:1;s:26:"/document-signature/create";i:2;s:34:"/document-signature/save-signature";i:3;s:23:"/document/view-document";i:4;s:15:"/document/index";i:5;s:16:"/document/update";i:6;s:16:"/document/create";i:7;s:19:"/document/getvragen";i:8;s:22:"/document-upload/index";i:9;s:18:"/document/dossiers";i:10;s:18:"/document/document";i:11;s:19:"/document/signature";i:12;s:14:"/document/view";i:13;s:22:"/document/viewdocument";i:14;s:18:"/document/viewfile";}}s:12:"custom_route";s:0:"";}s:6:"COOKIE";a:9:{s:18:"_identity-frontend";s:158:"ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a:2:{i:0;s:18:"_identity-frontend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:16:"sidebar-collapse";s:5:"false";s:17:"advanced-frontend";s:26:"3fp8o6megq4gr3o67bnqks3c7n";s:20:"advanced-backend-fmz";s:26:"b7df4be0nkp5g9vhkif794pfkf";s:21:"advanced-backend-test";s:26:"o4hu6edu0utm57lp4o2h9jjjj9";s:17:"_identity-backend";s:157:"946df6687b5fb74a9b0eaa13756e2019f90ea1fbcefd9b8ca4919bcb5352521aa:2:{i:0;s:17:"_identity-backend";i:1;s:46:"[1,"RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo",2592000]";}";s:13:"_csrf-backend";s:139:"0990b5f7dba8d91868f791f8029ebc7434255b71b1a85dc1ee8d4eff22c10b19a:2:{i:0;s:13:"_csrf-backend";i:1;s:32:"nqzMYT-ezqxHHZlR_7HRg_j9Y1RWPfxx";}";s:21:"advanced-frontend-fmz";s:26:"tkg1mvdevem4bjme5kqtr5uqg2";s:14:"_csrf-frontend";s:140:"f1520d10f90a11651376bef1453682b0b98ce980053a428d4c8b8eb96fa1ef01a:2:{i:0;s:14:"_csrf-frontend";i:1;s:32:"yzNq8LytO5crIYQuyaMfv0GNFlyggF73";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:4:{s:7:"__flash";a:1:{s:7:"success";i:-1;}s:4:"__id";i:1;s:9:"__authKey";s:32:"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW";s:7:"success";s:28:"Routes updated successfully.";}}";s:4:"user";s:1549:"a:5:{s:2:"id";i:1;s:8:"identity";a:12:{s:2:"id";s:1:"1";s:8:"username";s:11:"'SuperUser'";s:8:"auth_key";s:34:"'R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW'";s:13:"password_hash";s:62:"'$2y$13$uUSLwNI3so2bzilzzZdKJ.C1qmfyTdLRRT1XVP8jYO1c38iE6dfxS'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:17:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"1741788198";s:10:"updated_at";s:10:"1749661338";s:18:"verification_token";s:45:"'oZxacBEp5N7LZAqXc3s1haihOCLK8dLU_1741788198'";s:7:"role_id";s:1:"1";s:12:"access_token";s:66:"'CjYetJZp6PI5vAvKuqm6TDSC_2kYfe_LtOkrjMVtEo4IseTg3J-r-Gp3gMgH-pr7'";}s:10:"attributes";a:12:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:8:"Password";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:4:"Role";}i:11;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"68a356e18427a";s:3:"url";s:82:"http://localhost:8005/backoffice/index.php?r=role-functionality%2Froutes&role_id=4";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:3:"::1";s:4:"time";d:**********.466413;s:10:"statusCode";i:302;s:8:"sqlCount";i:44;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8954840;s:14:"processingTime";d:0.1813819408416748;}s:10:"exceptions";a:0:{}}