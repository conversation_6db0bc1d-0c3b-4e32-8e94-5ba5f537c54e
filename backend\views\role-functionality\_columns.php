<?php

use yii\helpers\Url;
use kartik\select2\Select2;
use common\models\Role; // Adjust namespace if needed
use yii\helpers\ArrayHelper;

return [
    [
        'class' => 'kartik\grid\CheckboxColumn',
        'width' => '20px',
    ],
    [
        'class' => 'kartik\grid\SerialColumn',
        'width' => '30px',
    ],
    [
        'class' => '\kartik\grid\DataColumn',
        'attribute' => 'role_id',
        'value' => 'role.name',
        'label' => 'Role',
        'filterType' => Select2::class,
        'filter' => ArrayHelper::map(Role::find()->all(), 'id', 'name'),
        'filterWidgetOptions' => [
            'pluginOptions' => [
                'allowClear' => true,
                'placeholder' => 'Select role...'
            ],
        ],
        'filterInputOptions' => ['placeholder' => 'Select role...'],
    ],
    [
        'class' => '\kartik\grid\DataColumn',
        'attribute' => 'route',
    ],
    // [
    //     'class' => '\kartik\grid\DataColumn',
    //     'attribute' => 'functionaliteit_id',
    //     'value' => 'functionality.name',
    //     'label' => 'Functionality'
    // ],
    [
        'class' => '\kartik\grid\DataColumn',
        'attribute' => 'active',
        'filter' => [1 => 1, 0 => 0],
        'filterInputOptions' => [
            'class' => 'form-control',
            'prompt' => 'Select active...',
        ],
    ],
    [
        'class' => '\kartik\grid\DataColumn',
        'attribute' => 'created_by',
        'value' => 'createdBy.username'
    ],
    [
        'class' => '\kartik\grid\DataColumn',
        'attribute' => 'updated_by',
        'value' => 'updatedBy.username'
    ],
    [
        'class' => 'kartik\grid\ActionColumn',
        'dropdown' => false,
        'noWrap' => 'true',
        'template' => '{view} {update} {delete}',
        'vAlign' => 'middle',
        'urlCreator' => function ($action, $model, $key, $index) {
            return Url::to([$action, 'id' => $key]);
        },
        'viewOptions' => ['role' => 'modal-remote', 'title' => Yii::t('yii2-ajaxcrud', 'View'), 'data-toggle' => 'tooltip', 'class' => 'btn btn-sm btn-outline-success'],
        'updateOptions' => ['role' => 'modal-remote', 'title' => Yii::t('yii2-ajaxcrud', 'Update'), 'data-toggle' => 'tooltip', 'class' => 'btn btn-sm btn-outline-primary'],
        'deleteOptions' => [
            'role' => 'modal-remote',
            'title' => Yii::t('yii2-ajaxcrud', 'Delete'),
            'class' => 'btn btn-sm btn-outline-danger',
            'data-confirm' => false,
            'data-method' => false, // for overide yii data api
            'data-request-method' => 'post',
            'data-toggle' => 'tooltip',
            'data-confirm-title' => Yii::t('yii2-ajaxcrud', 'Delete'),
            'data-confirm-message' => Yii::t('yii2-ajaxcrud', 'Delete Confirm')
        ],
    ],
];
